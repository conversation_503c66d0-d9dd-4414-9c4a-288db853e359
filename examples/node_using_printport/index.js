const { <PERSON><PERSON><PERSON> } = require("buffer"); // Importing the <PERSON>uffer class to handle binary data.
const Jimp = require("jimp"); // Library for image manipulation.
const noble = require('@abandonware/noble'); // Library for handling Bluetooth Low Energy (BLE) connections.
const printPort = require("../../lib/printport"); // Custom interface for specific operations.

let _bmpData = Buffer.alloc(0); // Variable to store binary image data.

/**
 * Connects to a BLE peripheral device.
 * @param {Object} peripheral - The BLE peripheral object.
 * @param {Function} onReady - Callback executed when the connection is ready.
 * @param {Function} onError - Callback executed when an error occurs.
 */
function connectToPeripheral(peripheral, onReady, onError) {
    console.log("Attempting to connect to the device...");

    peripheral.connect((error) => {
        if (error) {
            console.error("Connection error:", error);
            onError?.(error);
            return;
        }

        console.log(`Connection established with: ${peripheral.advertisement.localName}`);
        discoverCharacteristics(peripheral, onReady, onError);
    });

    peripheral.on("disconnect", () => {
        console.log("Device disconnected.");
    });
}

/**
 * Discovers the services and characteristics of a BLE peripheral.
 * @param {Object} peripheral - The BLE peripheral object.
 * @param {Function} onReady - Callback executed when the necessary characteristic is found.
 * @param {Function} onError - Callback executed when an error occurs.
 */
function discoverCharacteristics(peripheral, onReady, onError) {
    peripheral.discoverServices([], (err, services) => {
        if (err) {
            console.error("Service discovery error:", err);
            onError?.(err);
            return;
        }

        for (const service of services) {
            service.discoverCharacteristics([], (err, characteristics) => {
                let characteristic = null;
                if (err) {
                    console.error("Characteristic discovery error:", err);
                    onError?.(err);
                    return;
                }

                for (const char of characteristics) {
                    if (char.uuid.includes("ff02")) {
                        characteristic = char;
                        console.log(`Write characteristic configured: ${char.uuid}`);
                    }
                }

                if (characteristic) {
                    console.log("Device ready to use.");
                    onReady?.({
                        msg: "success",
                        deviceId: peripheral.id,
                        characteristic: characteristic
                    });
                } else {
                    console.error("Required write characteristic not found.");
                    onError?.(new Error("Write characteristic not found."));
                }
            });
        }
    });
}

/**
 * Processes an image file to prepare it for printing.
 * @param {string} filePath - Path to the image file.
 * @returns {Promise<Buffer>} A promise resolving to the processed image data as a buffer.
 */
async function prepareImage(filePath) {
    const bitmap = await Jimp.Jimp.read(filePath);
    const newWidth = 80;
    const newHeight = Math.round(newWidth * (bitmap.bitmap.height / bitmap.bitmap.width));

    bitmap.resize({ w: newWidth, h: newHeight }).greyscale();

    const imageData = {
        data: bitmap.bitmap.data,
        width: bitmap.bitmap.width,
        height: bitmap.bitmap.height,
    };

    return printPort.processImageData(imageData);
}

/**
 * Prepares and sends print data to a BLE device.
 * @param {Object} characteristic - The BLE characteristic for writing data.
 * @param {Buffer} imageBuffer - The buffer containing the image data to print.
 * @param {number} blueSize - The size of each data fragment.
 */
async function printImage(characteristic, imageBuffer, blueSize) {
    console.log("Preparing data for printing...");
    let data = Buffer.concat([
        printPort.startPrintjob(), // Starts the print job.
        printPort.adjustPositionAuto(0x51), // Adjusts initial position.
        imageBuffer, // Image data.
        printPort.stopPrintjob(), // Ends the print job.
        printPort.adjustPositionAuto(0x50), // Adjusts final position.
    ]);

    sendData(characteristic, blueSize, data, () => {
        console.log("Print data sent successfully.");
    });
}

/**
 * Sends data in chunks to the BLE device.
 * @param {Object} characteristic - The BLE characteristic for writing data.
 * @param {number} blueSize - The size of each data chunk.
 */
function sendDatabypac(characteristic, blueSize) {
    if (_bmpData.length === 0) return;

    const chunk = _bmpData.slice(0, Math.min(blueSize, _bmpData.length));
    characteristic.write(chunk, false, (error) => {
        if (error) {
            console.error("Error sending data:", error);
        } else {
            console.log("Data sent successfully");
        }
    });

    _bmpData = _bmpData.slice(chunk.length);
}

/**
 * Sends the complete data buffer in fragments to the BLE device.
 * @param {Object} characteristic - The BLE characteristic for writing data.
 * @param {number} blueSize - The size of each data fragment.
 * @param {Buffer} data - The complete data buffer to send.
 * @param {Function} callback - Callback executed after all data is sent.
 */
function sendData(characteristic, blueSize, data, callback) {
    _bmpData = Buffer.from(data);

    const interval = setInterval(() => {
        if (_bmpData.length > 0) {
            sendDatabypac(characteristic, blueSize);
        } else {
            clearInterval(interval);
            callback?.();
        }
    }, 30); // 30ms interval between chunks.
}

/**
 * Main function to execute the BLE printing process.
 * Scans for devices, connects to a specific one, and prints an image.
 */
async function main() {
    const imagePath = './assets/cc.bmp'; // Path to the image file.
    const imageBuffer = await prepareImage(imagePath);

    noble.startScanning([], false); // Starts scanning for BLE devices.

    noble.on('discover', (peripheral) => {
        // Filters devices by name.
        if (peripheral.advertisement.localName?.includes('P50')) {
            noble.stopScanning(); // Stops scanning when the target device is found.

            connectToPeripheral(peripheral, async ({ msg, devId, characteristic }) => {
                console.log("Successfully connected to the printer.");
                await printImage(characteristic, Buffer.from(imageBuffer), 90); // Sends the image data with a chunk size of 90 bytes.
            }, (error) => {
                console.error("Error connecting to the peripheral:", error);
            });
        }
    });
}

// Executes the main function.
main();
