const PrintPortHandlerNode = require('../handlers/printport-handler-node.js');
const noble = require("@abandonware/noble"); // Importa la clase

// Crea una instancia de la clase
const printPortHandler = new PrintPortHandlerNode();

/**
 * Función principal para ejecutar el proceso de impresión BLE.
 * Procesa la imagen, conecta al dispositivo BLE y envía los datos de impresión.
 */
async function main() {
    const imagePath = './assets/cc.bmp'; // Ruta de la imagen que deseas imprimir

    try {
        // Procesa la imagen
        const imageBuffer = await printPortHandler.prepareImage(imagePath);

        console.log("Iniciando escaneo de dispositivos BLE...");
        const noble = require('@abandonware/noble'); // Requiere noble para manejar BLE

        // Escanea dispositivos BLE
        noble.startScanning([], false);

        noble.on('discover', async (peripheral) => {
            // Filtra dispositivos por nombre
            if (peripheral.advertisement.localName?.includes('P50')) {
                noble.stopScanning();
                console.log(`Dispositivo encontrado: ${peripheral.advertisement.localName}`);

                try {
                    const characteristic = await printPortHandler.connectToPeripheral(peripheral);
                    console.log("Conectado exitosamente a la impresora.");
                    await printPortHandler.printImage(characteristic, imageBuffer, 90); // Envía los datos de la imagen con un tamaño de fragmento de 90 bytes.
                } catch (error) {
                    console.error("Error al conectar al dispositivo o enviar datos:", error);
                }
            }
        });

    } catch (error) {
        console.error("Error al procesar la imagen o iniciar el proceso de impresión:", error);
    }
}

// Ejecutar la función principal
main();
