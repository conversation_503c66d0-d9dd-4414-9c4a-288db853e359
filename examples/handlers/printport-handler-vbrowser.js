const printPort = require('../../lib/printport');
const bmp = require('bmp-js');
const { Buffer } = require('buffer');

class PrintportHandlerVBrowser {
    constructor() {
        this._bmpData = Buffer.alloc(0);
    }

    /**
     * Reads and processes a BMP image file to prepare it for printing.
     * @param {ArrayBuffer} fileBuffer - Buffer containing BMP image data.
     * @param {number} [newWidth=80] - Desired width for resizing the image. Defaults to 80.
     * @returns {Promise<Buffer>} Promise resolving with processed image data as a buffer.
     */
    async prepareImage(fileBuffer, newWidth = 80) {
        const bmpData = bmp.decode(Buffer.from(fileBuffer));
        const { data, width, height } = bmpData;

        const aspectRatio = height / width;
        const newHeight = Math.round(newWidth * aspectRatio);

        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const context = canvas.getContext('2d');

        const imageData = context.createImageData(width, height);

        for (let i = 0; i < data.length; i += 4) {
            const grayscale = (0.2126 * data[i]) +
                (0.7152 * data[i + 1]) +
                (0.0722 * data[i + 2]);

            imageData.data[i] = imageData.data[i + 1] = imageData.data[i + 2] = grayscale;
            imageData.data[i + 3] = 255; // Alpha
        }

        context.putImageData(imageData, 0, 0);

        const resizedCanvas = document.createElement('canvas');
        resizedCanvas.width = newWidth;
        resizedCanvas.height = newHeight;
        const resizedContext = resizedCanvas.getContext('2d');

        resizedContext.drawImage(canvas, 0, 0, newWidth, newHeight);

        const resizedImageData = resizedContext.getImageData(0, 0, newWidth, newHeight);
        const processedData = {
            data: resizedImageData.data,
            width: newWidth,
            height: newHeight,
        };

        return printPort.processImageData(processedData);
    }

    /**
     * Connects to a BLE device and retrieves the write characteristic.
     * @returns {Promise<BluetoothRemoteGATTCharacteristic>} Promise resolving with the write characteristic.
     */
    async connectToDeviceAndGetCharacteristic() {
        try {
            const device = await navigator.bluetooth.requestDevice({
                filters: [{ namePrefix: 'P50' }],
                optionalServices: ['0000ff00-0000-1000-8000-00805f9b34fb']
            });

            const server = await device.gatt.connect();
            const service = await server.getPrimaryService('0000ff00-0000-1000-8000-00805f9b34fb');
            const characteristic = await service.getCharacteristic('0000ff02-0000-1000-8000-00805f9b34fb');

            if (!characteristic.properties.writeWithoutResponse) {
                throw new Error('Characteristic does not support writeWithoutResponse');
            }

            return characteristic;
        } catch (error) {
            console.error('Error connecting to device:', error);
            throw error;
        }
    }

    /**
     * Prepares and sends print data to a BLE device.
     * @param {BluetoothRemoteGATTCharacteristic} characteristic - BLE write characteristic.
     * @param {Buffer} imageBuffer - Buffer containing image data to print.
     * @param {number} blueSize - Size of each data fragment.
     */
    async printImage(characteristic, imageBuffer, blueSize) {
        let data = Buffer.concat([
            Buffer.from(printPort.startPrintjob()),
            Buffer.from(printPort.adjustPositionAuto(0x51)),
            Buffer.from(new Uint8Array(imageBuffer)),
            Buffer.from(printPort.stopPrintjob()),
            Buffer.from(printPort.adjustPositionAuto(0x50)),
        ]);
        console.log("Data prepared, starting transmission...");
        await this.sendData(characteristic, blueSize, data);
        console.log("Print data sent successfully.");
    }

    /**
     * Sends data in fragments to the BLE device.
     * @param {BluetoothRemoteGATTCharacteristic} characteristic - BLE write characteristic.
     * @param {number} blueSize - Size of each data fragment.
     * @param {Buffer} data - Data to send.
     */
    async sendData(characteristic, blueSize, data) {
        this._bmpData = Buffer.from(data);

        while (this._bmpData.length > 0) {
            await this.sendDatabypac(characteristic, blueSize);
            await new Promise(resolve => setTimeout(resolve, 30)); // 30ms interval between fragments
        }
    }

    /**
     * Sends a single chunk of data to the BLE device.
     * @param {BluetoothRemoteGATTCharacteristic} characteristic - BLE characteristic to write data to.
     * @param {number} blueSize - Size of each data chunk.
     */
    async sendDatabypac(characteristic, blueSize) {
        if (this._bmpData.length === 0) return;

        const chunk = this._bmpData.slice(0, Math.min(blueSize, this._bmpData.length));

        try {
            await characteristic.writeValueWithoutResponse(chunk);
            console.log("Data sent successfully");
        } catch (error) {
            console.error("Error while sending data:", error);
        }

        this._bmpData = this._bmpData.slice(chunk.length);
    }

    /**
     * Discovers the services and characteristics of a BLE peripheral.
     * @param {Object} peripheral - The BLE peripheral object.
     * @param {Function} onReady - Callback executed when the necessary characteristic is found.
     * @param {Function} onError - Callback executed when an error occurs.
     */
    discoverCharacteristics(peripheral, onReady, onError) {
        peripheral.discoverServices([], (err, services) => {
            if (err) {
                console.error("Service discovery error:", err);
                onError?.(err);
                return;
            }

            for (const service of services) {
                service.discoverCharacteristics([], (err, characteristics) => {
                    let characteristic = null;
                    if (err) {
                        console.error("Characteristic discovery error:", err);
                        onError?.(err);
                        return;
                    }

                    for (const char of characteristics) {
                        if (char.uuid.includes("ff02")) {
                            characteristic = char;
                            console.log(`Write characteristic configured: ${char.uuid}`);
                        }
                    }

                    if (characteristic) {
                        console.log("Device ready to use.");
                        onReady?.({
                            msg: "success",
                            deviceId: peripheral.id,
                            characteristic: characteristic
                        });
                    } else {
                        console.error("Required write characteristic not found.");
                        onError?.(new Error("Write characteristic not found."));
                    }
                });
            }
        });
    }



}

module.exports = PrintportHandlerVBrowser;
