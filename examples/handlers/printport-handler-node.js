const { <PERSON><PERSON><PERSON> } = require("buffer"); // Importing the <PERSON><PERSON>er class to handle binary data.
const Jimp = require("jimp"); // Library for image manipulation.
const noble = require("@abandonware/noble"); // Library for handling Bluetooth Low Energy (BLE) connections.
const printPort = require("../../lib/printport"); // Custom interface for specific operations.

class PrintPortHandlerNode {
    constructor() {
        this._bmpData = Buffer.alloc(0);
    }

    /**
     * Connects to a BLE peripheral device.
     * @param {Object} peripheral - The BLE peripheral object.
     * @returns {Promise<Object>} Resolves with the BLE write characteristic.
     */
    async connectToPeripheral(peripheral) {
        return new Promise((resolve, reject) => {
            console.log("Attempting to connect to the device...");
            peripheral.connect((error) => {
                if (error) {
                    console.error("Connection error:", error);
                    reject(error);
                    return;
                }
                console.log(`Connection established with: ${peripheral.advertisement.localName}`);
                this.discoverCharacteristics(peripheral, resolve, reject);
            });

            peripheral.on("disconnect", () => {
                console.log("Device disconnected.");
            });
        });
    }

    /**
     * Discovers the services and characteristics of a BLE peripheral.
     * @param {Object} peripheral - The BLE peripheral object.
     * @param {Function} resolve - Resolves with the BLE write characteristic.
     * @param {Function} reject - Rejects in case of an error.
     */
    discoverCharacteristics(peripheral, resolve, reject) {
        peripheral.discoverServices([], (err, services) => {
            if (err) {
                console.error("Service discovery error:", err);
                reject(err);
                return;
            }

            for (const service of services) {
                service.discoverCharacteristics([], (err, characteristics) => {
                    if (err) {
                        console.error("Characteristic discovery error:", err);
                        reject(err);
                        return;
                    }

                    const characteristic = characteristics.find((char) =>
                        char.uuid.includes("ff02")
                    );

                    if (characteristic) {
                        console.log("Write characteristic configured:", characteristic.uuid);
                        resolve(characteristic);
                    } else {
                        reject(new Error("Required write characteristic not found."));
                    }
                });
            }
        });
    }

    /**
     * Processes an image file to prepare it for printing.
     * @param {string} filePath - Path to the image file.
     * @returns {Promise<Buffer>} A promise resolving to the processed image data as a buffer.
     */
    async prepareImage(filePath) {
        const bitmap = await Jimp.read(filePath);
        const newWidth = 80;
        const newHeight = Math.round(newWidth * (bitmap.bitmap.height / bitmap.bitmap.width));

        bitmap.resize(newWidth, newHeight).greyscale();

        const imageData = {
            data: bitmap.bitmap.data,
            width: bitmap.bitmap.width,
            height: bitmap.bitmap.height,
        };

        return printPort.processImageData(imageData);
    }

    /**
     * Prepares and sends print data to a BLE device.
     * @param {Object} characteristic - The BLE characteristic for writing data.
     * @param {Buffer} imageBuffer - The buffer containing the image data to print.
     * @param {number} blueSize - The size of each data fragment.
     */
    async printImage(characteristic, imageBuffer, blueSize) {
        const data = Buffer.concat([
            printPort.startPrintjob(),
            printPort.adjustPositionAuto(0x51),
            imageBuffer,
            printPort.stopPrintjob(),
            printPort.adjustPositionAuto(0x50),
        ]);

        console.log("Preparing data for printing...");
        this.sendData(characteristic, blueSize, data, () => {
            console.log("Print data sent successfully.");
        });
    }

    /**
     * Sends the complete data buffer in fragments to the BLE device.
     * @param {Object} characteristic - The BLE characteristic for writing data.
     * @param {number} blueSize - The size of each data fragment.
     * @param {Buffer} data - The complete data buffer to send.
     * @param {Function} callback - Callback executed after all data is sent.
     */
    sendData(characteristic, blueSize, data, callback) {
        this._bmpData = Buffer.from(data);

        const interval = setInterval(() => {
            if (this._bmpData.length > 0) {
                this.sendDatabypac(characteristic, blueSize);
            } else {
                clearInterval(interval);
                callback?.();
            }
        }, 30); // 30ms interval between chunks.
    }

    /**
     * Sends a single chunk of data to the BLE device.
     * @param {Object} characteristic - The BLE characteristic for writing data.
     * @param {number} blueSize - The size of each data chunk.
     */
    sendDatabypac(characteristic, blueSize) {
        if (this._bmpData.length === 0) return;

        const chunk = this._bmpData.slice(0, Math.min(blueSize, this._bmpData.length));

        characteristic.write(chunk, false, (error) => {
            if (error) {
                console.error("Error sending data:", error);
            } else {
                console.log("Data sent successfully");
            }
        });

        this._bmpData = this._bmpData.slice(chunk.length);
    }

}

module.exports = PrintPortHandlerNode;
