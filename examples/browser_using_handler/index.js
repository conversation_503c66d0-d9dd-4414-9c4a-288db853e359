const PrintportHandlerVBrowser = require('../handlers/printport-handler-vbrowser.js'); // Importa la clase

// Crea una instancia de la clase
const printPortHandler = new PrintportHandlerVBrowser();

/**
 * Función principal para ejecutar el proceso de impresión BLE.
 * Lee el archivo BMP, lo procesa, conecta al dispositivo BLE y envía los datos de impresión.
 */
async function main() {
    const fileInput = document.getElementById('bmpInput');
    const file = fileInput.files[0];

    if (!file) {
        console.error('No se ha seleccionado ningún archivo.');
        return;
    }

    // Lee el archivo como ArrayBuffer
    const fileBuffer = await file.arrayBuffer();

    try {
        // Usa la instancia para llamar a prepareImage
        const imageBuffer = await printPortHandler.prepareImage(fileBuffer, 80);
        const characteristic = await printPortHandler.connectToDeviceAndGetCharacteristic();
        console.log("Conectado exitosamente a la impresora.");
        await printPortHandler.printImage(characteristic, imageBuffer, 90); // Envía los datos de la imagen con un tamaño de fragmento de 90 bytes.
    } catch (error) {
        console.error("Error al procesar la imagen o conectar con el periférico:", error);
    }
}

// Exponer la función main al navegador
window.main = main;
