// index.js

const { Buffer } = require('buffer'); // Importa Buffer para manejar datos binarios
const bmp = require('bmp-js'); // Biblioteca para decodificar archivos BMP
const printPort = require('../../lib/printport'); // Tu módulo personalizado

// La API Web Bluetooth se accede a través de navigator.bluetooth; no es necesario requerirla

let _bmpData = Buffer.alloc(0); // Variable para almacenar los datos binarios de la imagen

/**
 * Lee y procesa un archivo de imagen BMP para prepararlo para la impresión.
 * @param {ArrayBuffer} fileBuffer - El buffer que contiene los datos del archivo BMP.
 * @returns {Promise<Buffer>} Una promesa que resuelve con los datos de imagen procesados como un buffer.
 */
async function prepareImage(fileBuffer) {
    // Decodifica el archivo BMP
    const bmpData = bmp.decode(Buffer.from(fileBuffer));
    const { data, width, height } = bmpData;

    // Configura las dimensiones para redimensionar
    const newWidth = 80;
    const aspectRatio = height / width;
    const newHeight = Math.round(newWidth * aspectRatio);

    // Crea un canvas para redimensionar
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const context = canvas.getContext('2d');

    // Crea ImageData con los datos originales
    const imageData = context.createImageData(width, height);

    // Procesa la imagen a escala de grises
    for (let i = 0; i < data.length; i += 4) {
        const grayscale = (0.2126 * data[i]) +
            (0.7152 * data[i + 1]) +
            (0.0722 * data[i + 2]);

        imageData.data[i] = imageData.data[i + 1] = imageData.data[i + 2] = grayscale;
        imageData.data[i + 3] = 255; // Alpha
    }

    // Dibuja la imagen en escala de grises en el canvas
    context.putImageData(imageData, 0, 0);

    // Crea un canvas redimensionado para la versión final
    const resizedCanvas = document.createElement('canvas');
    resizedCanvas.width = newWidth;
    resizedCanvas.height = newHeight;
    const resizedContext = resizedCanvas.getContext('2d');

    // Dibuja la imagen redimensionada en el canvas final
    resizedContext.drawImage(canvas, 0, 0, newWidth, newHeight);

    // Obtiene los datos de imagen redimensionados
    const resizedImageData = resizedContext.getImageData(0, 0, newWidth, newHeight);
    const processedData = {
        data: resizedImageData.data,
        width: newWidth,
        height: newHeight,
    };

    // Procesa los datos de imagen con printPort para la impresora
    return printPort.processImageData(processedData);
}


/**
 * Conecta al dispositivo BLE y obtiene la característica de escritura.
 * @returns {Promise<BluetoothRemoteGATTCharacteristic>} Una promesa que resuelve con la característica de escritura.
 */
async function connectToDeviceAndGetCharacteristic() {
    try {
        const device = await navigator.bluetooth.requestDevice({
            filters: [{ namePrefix: 'P50' }],
            optionalServices: ['0000ff00-0000-1000-8000-00805f9b34fb'] // Reemplaza con el UUID del servicio de tu dispositivo
        });

        const server = await device.gatt.connect();

        const service = await server.getPrimaryService('0000ff00-0000-1000-8000-00805f9b34fb'); // Reemplaza con el UUID del servicio
        const characteristic = await service.getCharacteristic('0000ff02-0000-1000-8000-00805f9b34fb'); // Reemplaza con el UUID de la característica

        // Verifica si la característica soporta escritura sin respuesta
        if (!characteristic.properties.writeWithoutResponse) {
            throw new Error('La característica no soporta escritura sin respuesta');
        }

        return characteristic;
    } catch (error) {
        console.error('Error al conectar con el dispositivo:', error);
        throw error;
    }
}

/**
 * Prepara y envía los datos de impresión a un dispositivo BLE.
 * @param {BluetoothRemoteGATTCharacteristic} characteristic - La característica BLE para escribir datos.
 * @param {Buffer} imageBuffer - El buffer que contiene los datos de la imagen a imprimir.
 * @param {number} blueSize - El tamaño de cada fragmento de datos.
 */
async function printImage(characteristic, imageBuffer, blueSize) {
    let data = Buffer.concat([
        Buffer.from(printPort.startPrintjob()),
        Buffer.from(printPort.adjustPositionAuto(0x51)),
        Buffer.from(new Uint8Array(imageBuffer)),
        Buffer.from(printPort.stopPrintjob()),
        Buffer.from(printPort.adjustPositionAuto(0x50)),
    ]);
    console.log("Datos preparados, iniciando envío...");
    await sendData(characteristic, blueSize, data);
    console.log("Datos de impresión enviados exitosamente.");
}

/**
 * Envía datos en fragmentos al dispositivo BLE.
 * @param {BluetoothRemoteGATTCharacteristic} characteristic - La característica BLE para escribir datos.
 * @param {number} blueSize - El tamaño de cada fragmento de datos.
 */
async function sendDatabypac(characteristic, blueSize) {
    if (_bmpData.length === 0) return;

    const chunk = _bmpData.slice(0, Math.min(blueSize, _bmpData.length));

    try {
        await characteristic.writeValueWithoutResponse(chunk);
        console.log("Datos enviados exitosamente");
    } catch (error) {
        console.error("Error al enviar datos:", error);
    }

    _bmpData = _bmpData.slice(chunk.length);
}

/**
 * Envía el buffer completo de datos en fragmentos al dispositivo BLE.
 * @param {BluetoothRemoteGATTCharacteristic} characteristic - La característica BLE para escribir datos.
 * @param {number} blueSize - El tamaño de cada fragmento de datos.
 * @param {Buffer} data - El buffer completo de datos a enviar.
 */
async function sendData(characteristic, blueSize, data) {
    _bmpData = Buffer.from(data);

    while (_bmpData.length > 0) {
        await sendDatabypac(characteristic, blueSize);
        // Espera un intervalo corto entre envíos
        await new Promise(resolve => setTimeout(resolve, 30)); // Intervalo de 30ms entre fragmentos
    }
}

async function previewGrayscale() {
    const fileInput = document.getElementById('bmpInput');
    const file = fileInput.files[0];

    if (!file) {
        console.error('No se ha seleccionado ningún archivo.');
        return;
    }

    // Lee el archivo como ArrayBuffer
    const fileBuffer = await file.arrayBuffer();

    // Decodifica el archivo BMP
    const bmpData = bmp.decode(Buffer.from(fileBuffer));
    const { data, width, height } = bmpData;

    // Configura el canvas para mostrar en escala de grises
    const grayscaleCanvas = document.getElementById('grayscaleCanvas');
    grayscaleCanvas.width = width;
    grayscaleCanvas.height = height;
    const context = grayscaleCanvas.getContext('2d');

    // Crea ImageData para la escala de grises
    const imageData = context.createImageData(width, height);
    for (let i = 0; i < data.length; i += 4) {
        const grayscale = (0.2126 * data[i]) +
            (0.7152 * data[i + 1]) +
            (0.0722 * data[i + 2]);

        imageData.data[i] = imageData.data[i + 1] = imageData.data[i + 2] = grayscale;
        imageData.data[i + 3] = 255; // Alpha
    }

    // Dibuja la imagen en escala de grises en el canvas
    context.putImageData(imageData, 0, 0);

    // Muestra el canvas
    grayscaleCanvas.style.display = 'block';
}

/**
 * Función principal para ejecutar el proceso de impresión BLE.
 * Lee el archivo BMP, lo procesa, conecta al dispositivo BLE y envía los datos de impresión.
 */
async function main() {
    const fileInput = document.getElementById('bmpInput');
    const file = fileInput.files[0];

    if (!file) {
        console.error('No se ha seleccionado ningún archivo.');
        return;
    }

    // Lee el archivo como ArrayBuffer
    const fileBuffer = await file.arrayBuffer();

    const imageBuffer = await prepareImage(fileBuffer);

    try {
        const characteristic = await connectToDeviceAndGetCharacteristic();
        console.log("Conectado exitosamente a la impresora.");
        await printImage(characteristic, imageBuffer, 90); // Envía los datos de la imagen con un tamaño de fragmento de 90 bytes.
    } catch (error) {
        console.error("Error al conectar con el periférico:", error);
    }
}

window.previewGrayscale = previewGrayscale;
window.main = main;
