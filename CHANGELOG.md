
# Changelog

## Version 1.0.4

### Updated
- **README.md**:
    - Added information specifying that the library has been tested with the Marklife P50S printer.

---

## Version 1.0.3

### Updated
- **README.md**:
    - Edited the content to improve clarity and structure.

---

## Version 1.0.2

### Added
- **Library Creation**:
    - Officially packaged the library for distribution via NPM.
    - Centralized all BLE printing logic in `printport.js` for shared functionality across environments.

- **Improved Handlers**:
    - Refined `printport-handler-node.js` for Node.js to process images, connect to BLE devices, and handle print jobs.
    - Refined `printport-handler-vbrowser.js` for browsers to utilize Web Bluetooth API for BLE connections.

- **Examples**:
    - Added structured examples for browser and Node.js usage:
        - `examples/node_handler`: Demonstrates printing via Node.js.
        - `examples/browser_using_handler`: Demonstrates browser-based BLE printing.

### Updated
- **README.md**:
    - Simplified instructions for using the library in Node.js and browser environments.
    - Highlighted additional dependencies (`noble`, `jimp`, `browserify`) based on the usage context.
    - Added detailed examples for each use case and clear instructions for bundling browser-based applications.

- **Folder Structure**:
    - Handlers for browser and Node.js moved to `lib/handlers`.
    - All library logic centralized under `lib/`.

- **Dependency Updates**:
    - Removed unnecessary internal bundling steps and externalized browser bundling to user-level via `browserify`.

### Fixed
- Resolved issues with BLE write characteristic detection in both handlers.
- Ensured compatibility of `bmp-js` with browser and Node.js environments.

### Notes
- Users now handle `browserify` bundling for browser projects to simplify the library distribution.
- Updated `package.json` to export only the core `printport.js` logic.

---

## Version 1.0.1

### Added
- **`index.js`**: New version using `browserify` to work in the browser. Replaces direct Node.js dependencies with a bundled solution.
- **`index.js`**: Retained as a reference for the initial Node.js implementation.
- **New `index.html` file**:
    - Includes buttons for:
        - **Preview**: Displays the grayscale image on a canvas.
        - **Print**: Connects to the BLE printer and sends the processed image.
    - Configured to communicate exclusively with `index.js`.

### Dependencies
- **`@abandonware/noble`**:
    - Used for managing Bluetooth Low Energy (BLE) connections in the Node.js version (`index.js`).
    - Not relevant for the browser-based version (`index.js`).

- **`bmp-js`**:
    - Library for decoding BMP images.
    - Used for both previewing and processing images for printing.

- **`jimp`**:
    - Previously used for image manipulation (resizing and grayscale conversion).
    - **Note**: No longer used in the new `index.js` implementation.

### DevDependencies
- **`@types/web-bluetooth`**:
    - Provides autocomplete and type definitions for the Web Bluetooth API when working with TypeScript or type-aware IDEs.

- **`browserify`**:
    - A bundling tool to use Node.js modules in the browser.
    - Transforms `index.js` to work with modern browsers.

---

### Usage
1. **Node.js**: For server environments, use `index.js` with dependencies such as `@abandonware/noble` and `jimp`.
2. **Browser**: Use `index.js` with `browserify` to bundle and deploy a browser-based solution.

---

## Version 1.0.0

### Added
- Implemented a functional BLE (Bluetooth Low Energy) version of the project.
- Integrated the following libraries:
    - **buffer**: For handling binary data.
    - **jimp**: For image manipulation.
    - **@abandonware/noble**: For managing BLE connections.
    - **printport**: Custom library for specific print operations.

### Features
1. **BLE Connection Management**:
    - Scans for BLE devices and filters based on names.
    - Establishes connection with the targeted BLE peripheral.
    - Discovers and configures the required write characteristics for communication.

2. **Image Processing**:
    - Reads and manipulates bitmap images using `jimp`.
    - Converts images to grayscale and resizes them for optimal printing.

3. **Printing Process**:
    - Processes image data into a format suitable for BLE devices.
    - Sends data in fragments (packets) to the BLE device.
    - Handles print job initiation, image data transmission, and print job termination.

4. **Asynchronous Execution**:
    - Implements asynchronous functions for image preparation and printing.
    - Sends data in chunks with customizable intervals to ensure reliable transmission.

---

### Usage
- The main script scans for BLE devices, connects to the printer, and prints a sample image from the path `./assets/cc.bmp`.
- Supports sending print data in 90-byte fragments.
