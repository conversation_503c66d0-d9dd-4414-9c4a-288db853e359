
# Marklife Label Printer Web Kit

**Tested with the Marklife P50S Printer**: This library has been specifically tested and verified to work with the Marklife P50S printer. While currently designed for this model, the library will be adapted to support additional Marklife printers as they are tested.

`marklife-label-printer-web-kit` is a JavaScript library designed to handle Bluetooth Low Energy (BLE) label printing using Marklife printers. This library supports both Node.js and browser environments.

**GitLab Repository**: [Marklife Label Printer Web Kit](https://gitlab.com/marklife/marklife-label-printer-web-kit)

## Table of Contents

- [Features](#features)
- [Installation](#installation)
- [Usage](#usage)
  - [Node.js Example](#nodejs-example)
  - [Browser Example](#browser-example)
- [Requirements](#requirements)
- [Contributing](#contributing)
- [License](#license)
- [Notes](#notes)

---

## Features

- **BLE Support**:
  - Scanning and connecting to Marklife BLE devices.
  - Sending print data in fragments for reliable communication.

- **Image Processing**:
  - BMP image handling for label printing.
  - Resizing and grayscale conversion.

- **Cross-Environment Support**:
  - Node.js operations using `noble` and `jimp`.
  - Browser operations using Web Bluetooth API.

---

## Installation

Install the library:

```bash
npm install marklife-label-printer-web-kit
```

### Additional Dependencies

For **Node.js**:
```bash
npm install @abandonware/noble jimp
```

For **Browser**:
```bash
npm install -g browserify
```

---

## Usage

### 1. Node.js Example

Install the required dependencies and use the following structure:

```javascript
// Example code for Node.js usage
```

### 2. Browser Example

Your `index.js` should look like this:

```javascript
// Example code for browser usage
```

---

## Requirements

### Node.js
- `@abandonware/noble`: For BLE device handling.
- `jimp`: For image processing.

### Browser
- Modern browsers with Web Bluetooth API support.
- `browserify`: For bundling dependencies.

---

## Contributing

Feel free to fork the repository and create pull requests with improvements or bug fixes.

---

## License

This library is licensed under a custom agreement between **MarkLife** and **MickeyGR**. Redistribution or modification is subject to strict limitations.

Please see the [LICENSE](./LICENSE) file for the full terms and conditions.

For permissions or inquiries, contact:
- **MarkLife**: <EMAIL> | [www.marklifeprinter.com](https://www.marklifeprinter.com)
- **MickeyGR**: <EMAIL> | [mickeygr.atokatl.dev](https://mickeygr.atokatl.dev)

---

## Notes

- Ensure your BLE device's name contains "P50" or update the examples to match your printer's name.
- The chunk size for `blueSize` is set to `90` in the examples, but this may need adjustment based on your printer's specifications.
