"use strict";
const {
    startPrintjob, stopPrintjob, adjustPositionAuto, adjustPosition, setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
} = require("./printer_commands/printer-commands");
const {processImageData} = require("./image_processing/image-processing");

module.exports = {
    processImageData,
    startPrintjob,
    stopPrintjob,
    adjustPositionAuto,
    adjustPosition,
    setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
};