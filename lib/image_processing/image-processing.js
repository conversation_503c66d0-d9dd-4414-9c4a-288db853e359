"use strict";

const { dada } = require("../data_processing/data-processing");

/**
 * Processes an image into a compressed and printable format for a printer.
 *
 * @param {Object} t - The image data object.
 * @param {number} t.height - Height of the image in pixels.
 * @param {number} t.width - Width of the image in pixels.
 * @param {Object} t.data - The raw image data (assumed to be RGBA format).
 * @returns {ArrayBuffer} A formatted and compressed image buffer ready for printing.
 */
function kakaImage(t) {
    // Image dimensions
    var e = t.height; // Height in pixels
    var n = t.width;  // Width in pixels

    // Access raw image data as a DataView for easier manipulation
    var i = new DataView(t.data.buffer);

    // Calculate the number of bytes per row (rounding up to the nearest byte)
    var r = parseInt((t.width + 7) / 8);

    // Prepare an ArrayBuffer for the processed image data
    var a = new ArrayBuffer(r * e);
    var s = new DataView(a);

    // Threshold for converting color to monochrome
    var o = 200;

    // Iterate through each pixel in the image
    for (var c = 0; c < t.height; c++) { // Loop through rows
        for (var u = 0; u < r; u++) {   // Loop through each byte in the row
            var f = 0; // Initialize byte for the current column
            for (var d = 0; d < 8; d++) { // Process up to 8 pixels per byte
                if (u * 8 + d < n) { // Check if within image bounds
                    // Get RGBA values for the current pixel
                    var v = i.getUint8((n * c + u * 8 + d) * 4);       // Red
                    var l = i.getUint8((n * c + u * 8 + d) * 4 + 1);   // Green
                    var g = i.getUint8((n * c + u * 8 + d) * 4 + 2);   // Blue
                    var h = i.getUint8((n * c + u * 8 + d) * 4 + 3);   // Alpha

                    // Convert RGB to grayscale
                    var w = (v + l + g) / 3;

                    // Apply threshold and check alpha channel
                    if (w != -1 && w <= o && h != 0) {
                        f |= 128 >> d; // Set the corresponding bit
                    }
                }
            }
            // Store the byte in the processed image buffer
            s.setUint8(r * c + u, f);
        }
    }

    // Compress the processed image data using the dada library
    var U = dada(a, { level: -1 });

    // Prepare the final command buffer
    var B = new ArrayBuffer(10 + U.length);
    var I = new DataView(B);

    // Add header information to the command buffer
    I.setUint8(0, 31);                   // Command type
    I.setUint8(1, 16);                   // Sub-command
    I.setUint8(2, r / 256);              // Width (high byte)
    I.setUint8(3, r % 256);              // Width (low byte)
    I.setUint8(4, e / 256);              // Height (high byte)
    I.setUint8(5, e % 256);              // Height (low byte)
    I.setUint8(6, (U.length >> 24) & 255); // Compressed data length (byte 1)
    I.setUint8(7, (U.length >> 16) & 255); // Compressed data length (byte 2)
    I.setUint8(8, (U.length >> 8) & 255);  // Compressed data length (byte 3)
    I.setUint8(9, U.length & 255);         // Compressed data length (byte 4)

    // Append compressed image data to the command buffer
    for (var c = 0; c < U.length; c++) {
        I.setUint8(10 + c, U[c]);
    }

    // Return the final command buffer
    return B;
}

/**
 * Asynchronously processes image data and prepares it for printing.
 *
 * @param {Object} imageData - The raw image data.
 * @returns {Promise<ArrayBuffer>} A promise resolving to the processed image buffer.
 */
async function processImageData(imageData) {
    return kakaImage(imageData);
}

module.exports = {
    processImageData,
};
