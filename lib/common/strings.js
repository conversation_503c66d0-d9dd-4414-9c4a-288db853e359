"use strict";
var utils = require("./common"),
    STR_APPLY_OK = !0,
    STR_APPLY_UIA_OK = !0;
try {
    String.fromCharCode.apply(null, [0]);
} catch (r) {
    STR_APPLY_OK = !1;
}
try {
    String.fromCharCode.apply(null, new Uint8Array(1));
} catch (r) {
    STR_APPLY_UIA_OK = !1;
}
for (var _utf8len = new utils.Buf8(256), q = 0; q < 256; q++)
    _utf8len[q] =
        252 <= q
            ? 6
            : 248 <= q
                ? 5
                : 240 <= q
                    ? 4
                    : 224 <= q
                        ? 3
                        : 192 <= q
                            ? 2
                            : 1;

function buf2binstring(r, t) {
    if (
        t < 65537 &&
        ((r.subarray && STR_APPLY_UIA_OK) || (!r.subarray && STR_APPLY_OK))
    )
        return String.fromCharCode.apply(null, utils.shrinkBuf(r, t));
    for (var n = "", e = 0; e < t; e++) n += String.fromCharCode(r[e]);
    return n;
}

(_utf8len[254] = _utf8len[254] = 1),
    (exports.string2buf = function (r) {
        var t,
            n,
            e,
            u,
            f,
            o = r.length,
            i = 0;
        for (u = 0; u < o; u++)
            55296 == (64512 & (n = r.charCodeAt(u))) &&
            u + 1 < o &&
            56320 == (64512 & (e = r.charCodeAt(u + 1))) &&
            ((n = 65536 + ((n - 55296) << 10) + (e - 56320)), u++),
                (i += n < 128 ? 1 : n < 2048 ? 2 : n < 65536 ? 3 : 4);
        for (t = new utils.Buf8(i), u = f = 0; f < i; u++)
            55296 == (64512 & (n = r.charCodeAt(u))) &&
            u + 1 < o &&
            56320 == (64512 & (e = r.charCodeAt(u + 1))) &&
            ((n = 65536 + ((n - 55296) << 10) + (e - 56320)), u++),
                (t[f++] =
                    n < 128
                        ? n
                        : ((t[f++] =
                            n < 2048
                                ? 192 | (n >>> 6)
                                : ((t[f++] =
                                    n < 65536
                                        ? 224 | (n >>> 12)
                                        : ((t[f++] = 240 | (n >>> 18)),
                                        128 | ((n >>> 12) & 63))),
                                128 | ((n >>> 6) & 63))),
                        128 | (63 & n)));
        return t;
    }),
    (exports.buf2binstring = function (r) {
        return buf2binstring(r, r.length);
    }),
    (exports.binstring2buf = function (r) {
        for (var t = new utils.Buf8(r.length), n = 0, e = t.length; n < e; n++)
            t[n] = r.charCodeAt(n);
        return t;
    }),
    (exports.buf2string = function (r, t) {
        var n,
            e,
            u,
            f,
            o = t || r.length,
            i = new Array(2 * o);
        for (n = e = 0; n < o;)
            if ((u = r[n++]) < 128) i[e++] = u;
            else if (4 < (f = _utf8len[u])) (i[e++] = 65533), (n += f - 1);
            else {
                for (u &= 2 === f ? 31 : 3 === f ? 15 : 7; 1 < f && n < o;)
                    (u = (u << 6) | (63 & r[n++])), f--;
                i[e++] =
                    1 < f
                        ? 65533
                        : u < 65536
                            ? u
                            : ((u -= 65536),
                                (i[e++] = 55296 | ((u >> 10) & 1023)),
                            56320 | (1023 & u));
            }
        return buf2binstring(i, e);
    }),
    (exports.utf8border = function (r, t) {
        var n;
        for (
            (t = t || r.length) > r.length && (t = r.length), n = t - 1;
            0 <= n && 128 == (192 & r[n]);
        )
            n--;
        return n < 0 ? t : 0 === n ? t : n + _utf8len[r[n]] > t ? n : t;
    });
