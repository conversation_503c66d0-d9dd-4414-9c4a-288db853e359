"use strict";
var configuration_table,
    utils = require("./common"),
    D_CODES = 30,
    HEAP_SIZE = 2 * (L_CODES = 286) + 1;

function err(t, e) {
    return e;
}

function rank(t) {
    return (t << 1) - (4 < t ? 9 : 0);
}

function zero(t) {
    for (var e = t.length; 0 <= --e;) t[e] = 0;
}

function flush_pending(t) {
    var e = t.state,
        a = e.pending;
    a > t.avail_out && (a = t.avail_out),
    0 !== a &&
    (utils.arraySet(t.output, e.pending_buf, e.pending_out, a, t.next_out),
        (t.next_out += a),
        (e.pending_out += a),
        (t.total_out += a),
        (t.avail_out -= a),
        (e.pending -= a),
    0 === e.pending && (e.pending_out = 0));
}

function flush_block_only(t, e) {
    _tr_flush_block(
        t,
        0 <= t.block_start ? t.block_start : -1,
        t.strstart - t.block_start,
        e
    ),
        (t.block_start = t.strstart),
        flush_pending(t.strm);
}

function put_byte(t, e) {
    t.pending_buf[t.pending++] = e;
}

function putShortMSB(t, e) {
    (t.pending_buf[t.pending++] = (e >>> 8) & 255),
        (t.pending_buf[t.pending++] = 255 & e);
}

function read_buf(t, e, a, _) {
    var s = t.avail_in;
    return (
        _ < s && (s = _),
            0 === s
                ? 0
                : ((t.avail_in -= s),
                    utils.arraySet(e, t.input, t.next_in, s, a),
                    1 === t.state.wrap
                        ? (t.adler = adler32(t.adler, e, s, a))
                        : 2 === t.state.wrap && (t.adler = crc32(t.adler, e, s, a)),
                    (t.next_in += s),
                    (t.total_in += s),
                    s)
    );
}

function longest_match(t, e) {
    var a,
        _,
        s = t.max_chain_length,
        n = t.strstart,
        r = t.prev_length,
        i = t.nice_match,
        l = t.strstart > t.w_size - 262 ? t.strstart - (t.w_size - 262) : 0,
        d = t.window,
        h = t.w_mask,
        o = t.prev,
        u = t.strstart + 258,
        c = d[n + r - 1],
        f = d[n + r];
    t.prev_length >= t.good_match && (s >>= 2),
    i > t.lookahead && (i = t.lookahead);
    do {
        if (
            d[(a = e) + r] === f &&
            d[a + r - 1] === c &&
            d[a] === d[n] &&
            d[++a] === d[n + 1]
        ) {
            (n += 2), a++;
            do {
            } while (
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                n < u
                );
            if (((_ = 258 - (u - n)), (n = u - 258), r < _)) {
                if (((t.match_start = e), i <= (r = _))) break;
                (c = d[n + r - 1]), (f = d[n + r]);
            }
        }
    } while ((e = o[e & h]) > l && 0 != --s);
    return r <= t.lookahead ? r : t.lookahead;
}

function fill_window(t) {
    var e,
        a,
        _,
        s,
        n,
        r = t.w_size;
    do {
        if (
            ((s = t.window_size - t.lookahead - t.strstart),
            t.strstart >= r + (r - 262))
        ) {
            for (
                utils.arraySet(t.window, t.window, r, r, 0),
                    t.match_start -= r,
                    t.strstart -= r,
                    t.block_start -= r,
                    e = a = t.hash_size;
                (_ = t.head[--e]), (t.head[e] = r <= _ ? _ - r : 0), --a;
            ) ;
            for (
                e = a = r;
                (_ = t.prev[--e]), (t.prev[e] = r <= _ ? _ - r : 0), --a;
            ) ;
            s += r;
        }
        if (0 === t.strm.avail_in) break;
        if (
            ((a = read_buf(t.strm, t.window, t.strstart + t.lookahead, s)),
                (t.lookahead += a),
            3 <= t.lookahead + t.insert)
        )
            for (
                n = t.strstart - t.insert,
                    t.ins_h = t.window[n],
                    t.ins_h = ((t.ins_h << t.hash_shift) ^ t.window[n + 1]) & t.hash_mask;
                t.insert &&
                ((t.ins_h =
                    ((t.ins_h << t.hash_shift) ^ t.window[n + 3 - 1]) & t.hash_mask),
                    (t.prev[n & t.w_mask] = t.head[t.ins_h]),
                    (t.head[t.ins_h] = n),
                    n++,
                    t.insert--,
                    !(t.lookahead + t.insert < 3));
            ) ;
    } while (t.lookahead < 262 && 0 !== t.strm.avail_in);
}

function dudu_stored(t, e) {
    var a = 65535;
    for (a > t.pending_buf_size - 5 && (a = t.pending_buf_size - 5); ;) {
        if (t.lookahead <= 1) {
            if ((fill_window(t), 0 === t.lookahead && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        (t.strstart += t.lookahead), (t.lookahead = 0);
        var _ = t.block_start + a;
        if (
            (0 === t.strstart || t.strstart >= _) &&
            ((t.lookahead = t.strstart - _),
                (t.strstart = _),
                flush_block_only(t, !1),
            0 === t.strm.avail_out)
        )
            return 1;
        if (
            t.strstart - t.block_start >= t.w_size - 262 &&
            (flush_block_only(t, !1), 0 === t.strm.avail_out)
        )
            return 1;
    }
    return (
        (t.insert = 0),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : (t.strstart > t.block_start &&
                (flush_block_only(t, !1), t.strm.avail_out),
                    1)
    );
}

function dudu_fast(t, e) {
    for (var a, _; ;) {
        if (t.lookahead < 262) {
            if ((fill_window(t), t.lookahead < 262 && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        if (
            ((a = 0),
            3 <= t.lookahead &&
            ((t.ins_h =
                ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                t.hash_mask),
                (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                (t.head[t.ins_h] = t.strstart)),
            0 !== a &&
            t.strstart - a <= t.w_size - 262 &&
            (t.match_length = longest_match(t, a)),
            3 <= t.match_length)
        )
            if (
                ((_ = _tr_tally(t, t.strstart - t.match_start, t.match_length - 3)),
                    (t.lookahead -= t.match_length),
                t.match_length <= t.max_lazy_match && 3 <= t.lookahead)
            ) {
                for (
                    t.match_length--;
                    t.strstart++,
                        (t.ins_h =
                            ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                            t.hash_mask),
                        (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                        (t.head[t.ins_h] = t.strstart),
                    0 != --t.match_length;
                ) ;
                t.strstart++;
            } else
                (t.strstart += t.match_length),
                    (t.match_length = 0),
                    (t.ins_h = t.window[t.strstart]),
                    (t.ins_h =
                        ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 1]) &
                        t.hash_mask);
        else
            (_ = _tr_tally(t, 0, t.window[t.strstart])), t.lookahead--, t.strstart++;
        if (_ && (flush_block_only(t, !1), 0 === t.strm.avail_out)) return 1;
    }
    return (
        (t.insert = t.strstart < 2 ? t.strstart : 2),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function dudu_slow(t, e) {
    for (var a, _, s; ;) {
        if (t.lookahead < 262) {
            if ((fill_window(t), t.lookahead < 262 && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        if (
            ((a = 0),
            3 <= t.lookahead &&
            ((t.ins_h =
                ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                t.hash_mask),
                (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                (t.head[t.ins_h] = t.strstart)),
                (t.prev_length = t.match_length),
                (t.prev_match = t.match_start),
                (t.match_length = 2),
            0 !== a &&
            t.prev_length < t.max_lazy_match &&
            t.strstart - a <= t.w_size - 262 &&
            ((t.match_length = longest_match(t, a)),
            t.match_length <= 5 &&
            (1 === t.strategy ||
                (3 === t.match_length && 4096 < t.strstart - t.match_start)) &&
            (t.match_length = 2)),
            3 <= t.prev_length && t.match_length <= t.prev_length)
        ) {
            for (
                s = t.strstart + t.lookahead - 3,
                    _ = _tr_tally(t, t.strstart - 1 - t.prev_match, t.prev_length - 3),
                    t.lookahead -= t.prev_length - 1,
                    t.prev_length -= 2;
                ++t.strstart <= s &&
                ((t.ins_h =
                    ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                    t.hash_mask),
                    (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                    (t.head[t.ins_h] = t.strstart)),
                0 != --t.prev_length;
            ) ;
            if (
                ((t.match_available = 0),
                    (t.match_length = 2),
                    t.strstart++,
                _ && (flush_block_only(t, !1), 0 === t.strm.avail_out))
            )
                return 1;
        } else if (t.match_available) {
            if (
                ((_ = _tr_tally(t, 0, t.window[t.strstart - 1])) &&
                flush_block_only(t, !1),
                    t.strstart++,
                    t.lookahead--,
                0 === t.strm.avail_out)
            )
                return 1;
        } else (t.match_available = 1), t.strstart++, t.lookahead--;
    }
    return (
        t.match_available &&
        ((_ = _tr_tally(t, 0, t.window[t.strstart - 1])),
            (t.match_available = 0)),
            (t.insert = t.strstart < 2 ? t.strstart : 2),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function dudu_rle(t, e) {
    for (var a, _, s, n, r = t.window; ;) {
        if (t.lookahead <= 258) {
            if ((fill_window(t), t.lookahead <= 258 && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        if (
            ((t.match_length = 0),
            3 <= t.lookahead &&
            0 < t.strstart &&
            (_ = r[(s = t.strstart - 1)]) === r[++s] &&
            _ === r[++s] &&
            _ === r[++s])
        ) {
            n = t.strstart + 258;
            do {
            } while (
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                s < n
                );
            (t.match_length = 258 - (n - s)),
            t.match_length > t.lookahead && (t.match_length = t.lookahead);
        }
        if (
            (3 <= t.match_length
                ? ((a = _tr_tally(t, 1, t.match_length - 3)),
                    (t.lookahead -= t.match_length),
                    (t.strstart += t.match_length),
                    (t.match_length = 0))
                : ((a = _tr_tally(t, 0, t.window[t.strstart])),
                    t.lookahead--,
                    t.strstart++),
            a && (flush_block_only(t, !1), 0 === t.strm.avail_out))
        )
            return 1;
    }
    return (
        (t.insert = 0),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function dudu_huff(t, e) {
    for (var a; ;) {
        if (0 === t.lookahead && (fill_window(t), 0 === t.lookahead)) {
            if (0 === e) return 1;
            break;
        }
        if (
            ((t.match_length = 0),
                (a = _tr_tally(t, 0, t.window[t.strstart])),
                t.lookahead--,
                t.strstart++,
            a && (flush_block_only(t, !1), 0 === t.strm.avail_out))
        )
            return 1;
    }
    return (
        (t.insert = 0),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function Config(t, e, a, _, s) {
    (this.good_length = t),
        (this.max_lazy = e),
        (this.nice_length = a),
        (this.max_chain = _),
        (this.func = s);
}

function lm_init(t) {
    (t.window_size = 2 * t.w_size),
        zero(t.head),
        (t.max_lazy_match = configuration_table[t.level].max_lazy),
        (t.good_match = configuration_table[t.level].good_length),
        (t.nice_match = configuration_table[t.level].nice_length),
        (t.max_chain_length = configuration_table[t.level].max_chain),
        (t.strstart = 0),
        (t.block_start = 0),
        (t.lookahead = 0),
        (t.insert = 0),
        (t.match_length = t.prev_length = 2),
        (t.match_available = 0),
        (t.ins_h = 0);
}

function duduState() {
    (this.strm = null),
        (this.status = 0),
        (this.pending_buf = null),
        (this.pending_buf_size = 0),
        (this.pending_out = 0),
        (this.pending = 0),
        (this.wrap = 0),
        (this.gzhead = null),
        (this.gzindex = 0),
        (this.method = 8),
        (this.last_flush = -1),
        (this.w_size = 0),
        (this.w_bits = 0),
        (this.w_mask = 0),
        (this.window = null),
        (this.window_size = 0),
        (this.prev = null),
        (this.head = null),
        (this.ins_h = 0),
        (this.hash_size = 0),
        (this.hash_bits = 0),
        (this.hash_mask = 0),
        (this.hash_shift = 0),
        (this.block_start = 0),
        (this.match_length = 0),
        (this.prev_match = 0),
        (this.match_available = 0),
        (this.strstart = 0),
        (this.match_start = 0),
        (this.lookahead = 0),
        (this.prev_length = 0),
        (this.max_chain_length = 0),
        (this.max_lazy_match = 0),
        (this.level = 0),
        (this.strategy = 0),
        (this.good_match = 0),
        (this.nice_match = 0),
        (this.dyn_ltree = new utils.Buf16(2 * HEAP_SIZE)),
        (this.dyn_dtree = new utils.Buf16(2 * (2 * D_CODES + 1))),
        (this.bl_tree = new utils.Buf16(78)),
        zero(this.dyn_ltree),
        zero(this.dyn_dtree),
        zero(this.bl_tree),
        (this.l_desc = null),
        (this.d_desc = null),
        (this.bl_desc = null),
        (this.bl_count = new utils.Buf16(16)),
        (this.heap = new utils.Buf16(2 * L_CODES + 1)),
        zero(this.heap),
        (this.heap_len = 0),
        (this.heap_max = 0),
        (this.depth = new utils.Buf16(2 * L_CODES + 1)),
        zero(this.depth),
        (this.l_buf = 0),
        (this.lit_bufsize = 0),
        (this.last_lit = 0),
        (this.d_buf = 0),
        (this.opt_len = 0),
        (this.static_len = 0),
        (this.matches = 0),
        (this.insert = 0),
        (this.bi_buf = 0),
        (this.bi_valid = 0);
}

function duduResetKeep(t) {
    var e;
    return t && t.state
        ? ((t.total_in = t.total_out = 0),
            (t.data_type = 2),
            ((e = t.state).pending = 0),
            (e.pending_out = 0),
        e.wrap < 0 && (e.wrap = -e.wrap),
            (e.status = e.wrap ? 42 : 113),
            (t.adler = 2 === e.wrap ? 0 : 1),
            (e.last_flush = 0),
            _tr_init(e),
            0)
        : err(t, -2);
}

function duduReset(t) {
    var e = duduResetKeep(t);
    return 0 === e && lm_init(t.state), e;
}

function duduSetHeader(t, e) {
    return t && t.state
        ? 2 !== t.state.wrap
            ? -2
            : ((t.state.gzhead = e), 0)
        : -2;
}

function duduInit2(t, e, a, _, s, n) {
    if (!t) return -2;
    var r = 1;
    if (
        (-1 === e && (e = 6),
            _ < 0 ? ((r = 0), (_ = -_)) : 15 < _ && ((r = 2), (_ -= 16)),
        s < 1 ||
        9 < s ||
        8 !== a ||
        _ < 8 ||
        15 < _ ||
        e < 0 ||
        9 < e ||
        n < 0 ||
        4 < n)
    )
        return err(t, -2);
    8 === _ && (_ = 9);
    var i = new duduState();
    return (
        ((t.state = i).strm = t),
            (i.wrap = r),
            (i.gzhead = null),
            (i.w_bits = _),
            (i.w_size = 1 << i.w_bits),
            (i.w_mask = i.w_size - 1),
            (i.hash_bits = s + 7),
            (i.hash_size = 1 << i.hash_bits),
            (i.hash_mask = i.hash_size - 1),
            (i.hash_shift = ~~((i.hash_bits + 3 - 1) / 3)),
            (i.window = new utils.Buf8(2 * i.w_size)),
            (i.head = new utils.Buf16(i.hash_size)),
            (i.prev = new utils.Buf16(i.w_size)),
            (i.lit_bufsize = 1 << (s + 6)),
            (i.pending_buf_size = 4 * i.lit_bufsize),
            (i.pending_buf = new utils.Buf8(i.pending_buf_size)),
            (i.d_buf = 1 * i.lit_bufsize),
            (i.l_buf = 3 * i.lit_bufsize),
            (i.level = e),
            (i.strategy = n),
            (i.method = a),
            duduReset(t)
    );
}

function duduInit(t, e) {
    return duduInit2(t, e, 8, 10, 8, 0);
}

function dudu(t, e) {
    var a, _, s, n;
    if (!t || !t.state || 5 < e || e < 0) return t ? err(t, -2) : -2;
    if (
        ((_ = t.state),
        !t.output ||
        (!t.input && 0 !== t.avail_in) ||
        (666 === _.status && 4 !== e))
    )
        return err(t, 0 === t.avail_out ? -5 : -2);
    if (((_.strm = t), (a = _.last_flush), (_.last_flush = e), 42 === _.status))
        if (2 === _.wrap)
            (t.adler = 0),
                put_byte(_, 31),
                put_byte(_, 139),
                put_byte(_, 8),
                _.gzhead
                    ? (put_byte(
                        _,
                        (_.gzhead.text ? 1 : 0) +
                        (_.gzhead.hcrc ? 2 : 0) +
                        (_.gzhead.extra ? 4 : 0) +
                        (_.gzhead.name ? 8 : 0) +
                        (_.gzhead.comment ? 16 : 0)
                    ),
                        put_byte(_, 255 & _.gzhead.time),
                        put_byte(_, (_.gzhead.time >> 8) & 255),
                        put_byte(_, (_.gzhead.time >> 16) & 255),
                        put_byte(_, (_.gzhead.time >> 24) & 255),
                        put_byte(
                            _,
                            9 === _.level ? 2 : 2 <= _.strategy || _.level < 2 ? 4 : 0
                        ),
                        put_byte(_, 255 & _.gzhead.os),
                    _.gzhead.extra &&
                    _.gzhead.extra.length &&
                    (put_byte(_, 255 & _.gzhead.extra.length),
                        put_byte(_, (_.gzhead.extra.length >> 8) & 255)),
                    _.gzhead.hcrc &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending, 0)),
                        (_.gzindex = 0),
                        (_.status = 69))
                    : (put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(
                            _,
                            9 === _.level ? 2 : 2 <= _.strategy || _.level < 2 ? 4 : 0
                        ),
                        put_byte(_, 3),
                        (_.status = 113));
        else {
            var r = (8 + ((_.w_bits - 8) << 4)) << 8;
            (r |=
                (2 <= _.strategy || _.level < 2
                    ? 0
                    : _.level < 6
                        ? 1
                        : 6 === _.level
                            ? 2
                            : 3) << 6),
            0 !== _.strstart && (r |= 32),
                (r += 31 - (r % 31)),
                (_.status = 113),
                putShortMSB(_, r),
            0 !== _.strstart &&
            (putShortMSB(_, t.adler >>> 16), putShortMSB(_, 65535 & t.adler)),
                (t.adler = 1);
        }
    if (69 === _.status)
        if (_.gzhead.extra) {
            for (
                s = _.pending;
                _.gzindex < (65535 & _.gzhead.extra.length) &&
                (_.pending !== _.pending_buf_size ||
                    (_.gzhead.hcrc &&
                    _.pending > s &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
                        flush_pending(t),
                        (s = _.pending),
                    _.pending !== _.pending_buf_size));
            )
                put_byte(_, 255 & _.gzhead.extra[_.gzindex]), _.gzindex++;
            _.gzhead.hcrc &&
            _.pending > s &&
            (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
            _.gzindex === _.gzhead.extra.length &&
            ((_.gzindex = 0), (_.status = 73));
        } else _.status = 73;
    if (73 === _.status)
        if (_.gzhead.name) {
            s = _.pending;
            do {
                if (
                    _.pending === _.pending_buf_size &&
                    (_.gzhead.hcrc &&
                    _.pending > s &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
                        flush_pending(t),
                        (s = _.pending),
                    _.pending === _.pending_buf_size)
                ) {
                    n = 1;
                    break;
                }
                put_byte(
                    _,
                    (n =
                        _.gzindex < _.gzhead.name.length
                            ? 255 & _.gzhead.name.charCodeAt(_.gzindex++)
                            : 0)
                );
            } while (0 !== n);
            _.gzhead.hcrc &&
            _.pending > s &&
            (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
            0 === n && ((_.gzindex = 0), (_.status = 91));
        } else _.status = 91;
    if (91 === _.status)
        if (_.gzhead.comment) {
            s = _.pending;
            do {
                if (
                    _.pending === _.pending_buf_size &&
                    (_.gzhead.hcrc &&
                    _.pending > s &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
                        flush_pending(t),
                        (s = _.pending),
                    _.pending === _.pending_buf_size)
                ) {
                    n = 1;
                    break;
                }
                put_byte(
                    _,
                    (n =
                        _.gzindex < _.gzhead.comment.length
                            ? 255 & _.gzhead.comment.charCodeAt(_.gzindex++)
                            : 0)
                );
            } while (0 !== n);
            _.gzhead.hcrc &&
            _.pending > s &&
            (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
            0 === n && (_.status = 103);
        } else _.status = 103;
    if (
        (103 === _.status &&
        (_.gzhead.hcrc
            ? (_.pending + 2 > _.pending_buf_size && flush_pending(t),
            _.pending + 2 <= _.pending_buf_size &&
            (put_byte(_, 255 & t.adler),
                put_byte(_, (t.adler >> 8) & 255),
                (t.adler = 0),
                (_.status = 113)))
            : (_.status = 113)),
        0 !== _.pending)
    ) {
        if ((flush_pending(t), 0 === t.avail_out)) return (_.last_flush = -1), 0;
    } else if (0 === t.avail_in && rank(e) <= rank(a) && 4 !== e)
        return err(t, -5);
    if (666 === _.status && 0 !== t.avail_in) return err(t, -5);
    if (0 !== t.avail_in || 0 !== _.lookahead || (0 !== e && 666 !== _.status)) {
        var i =
            2 === _.strategy
                ? dudu_huff(_, e)
                : 3 === _.strategy
                    ? dudu_rle(_, e)
                    : configuration_table[_.level].func(_, e);
        if (((3 !== i && 4 !== i) || (_.status = 666), 1 === i || 3 === i))
            return 0 === t.avail_out && (_.last_flush = -1), 0;
        if (
            2 === i &&
            (1 === e
                ? _tr_align(_)
                : 5 !== e &&
                (_tr_stored_block(_, 0, 0, !1),
                3 === e &&
                (zero(_.head),
                0 === _.lookahead &&
                ((_.strstart = 0), (_.block_start = 0), (_.insert = 0)))),
                flush_pending(t),
            0 === t.avail_out)
        )
            return (_.last_flush = -1), 0;
    }
    return 4 !== e
        ? 0
        : _.wrap <= 0
            ? 1
            : (2 === _.wrap
                ? (put_byte(_, 255 & t.adler),
                    put_byte(_, (t.adler >> 8) & 255),
                    put_byte(_, (t.adler >> 16) & 255),
                    put_byte(_, (t.adler >> 24) & 255),
                    put_byte(_, 255 & t.total_in),
                    put_byte(_, (t.total_in >> 8) & 255),
                    put_byte(_, (t.total_in >> 16) & 255),
                    put_byte(_, (t.total_in >> 24) & 255))
                : (putShortMSB(_, t.adler >>> 16), putShortMSB(_, 65535 & t.adler)),
                flush_pending(t),
            0 < _.wrap && (_.wrap = -_.wrap),
                0 !== _.pending ? 0 : 1);
}

function duduEnd(t) {
    var e;
    return t && t.state
        ? 42 !== (e = t.state.status) &&
        69 !== e &&
        73 !== e &&
        91 !== e &&
        103 !== e &&
        113 !== e &&
        666 !== e
            ? err(t, -2)
            : ((t.state = null), 113 === e ? err(t, -3) : 0)
        : -2;
}

function duduSetDictionary(t, e) {
    var a,
        _,
        s,
        n,
        r,
        i,
        l,
        d,
        h = e.length;
    if (!t || !t.state) return -2;
    if (
        2 === (n = (a = t.state).wrap) ||
        (1 === n && 42 !== a.status) ||
        a.lookahead
    )
        return -2;
    for (
        1 === n && (t.adler = adler32(t.adler, e, h, 0)),
            a.wrap = 0,
        h >= a.w_size &&
        (0 === n &&
        (zero(a.head), (a.strstart = 0), (a.block_start = 0), (a.insert = 0)),
            (d = new utils.Buf8(a.w_size)),
            utils.arraySet(d, e, h - a.w_size, a.w_size, 0),
            (e = d),
            (h = a.w_size)),
            r = t.avail_in,
            i = t.next_in,
            l = t.input,
            t.avail_in = h,
            t.next_in = 0,
            t.input = e,
            fill_window(a);
        3 <= a.lookahead;
    ) {
        for (
            _ = a.strstart, s = a.lookahead - 2;
            (a.ins_h =
                ((a.ins_h << a.hash_shift) ^ a.window[_ + 3 - 1]) & a.hash_mask),
                (a.prev[_ & a.w_mask] = a.head[a.ins_h]),
                (a.head[a.ins_h] = _),
                _++,
                --s;
        ) ;
        (a.strstart = _), (a.lookahead = 2), fill_window(a);
    }
    return (
        (a.strstart += a.lookahead),
            (a.block_start = a.strstart),
            (a.insert = a.lookahead),
            (a.lookahead = 0),
            (a.match_length = a.prev_length = 2),
            (a.match_available = 0),
            (t.next_in = i),
            (t.input = l),
            (t.avail_in = r),
            (a.wrap = n),
            0
    );
}

function adler32(t, e, a, _) {
    for (
        var s = (65535 & t) | 0, n = ((t >>> 16) & 65535) | 0, r = 0;
        0 !== a;
    ) {
        for (
            a -= r = 2e3 < a ? 2e3 : a;
            (n = (n + (s = (s + e[_++]) | 0)) | 0), --r;
        ) ;
        (s %= 65521), (n %= 65521);
    }
    return s | (n << 16) | 0;
}

function makeTable() {
    for (var t, e = [], a = 0; a < 256; a++) {
        t = a;
        for (var _ = 0; _ < 8; _++) t = 1 & t ? 3988292384 ^ (t >>> 1) : t >>> 1;
        e[a] = t;
    }
    return e;
}

configuration_table = [
    new Config(0, 0, 0, 0, dudu_stored),
    new Config(4, 4, 8, 4, dudu_fast),
    new Config(4, 5, 16, 8, dudu_fast),
    new Config(4, 6, 32, 32, dudu_fast),
    new Config(4, 4, 16, 16, dudu_slow),
    new Config(8, 16, 32, 32, dudu_slow),
    new Config(8, 16, 128, 128, dudu_slow),
    new Config(8, 32, 128, 256, dudu_slow),
    new Config(32, 128, 258, 1024, dudu_slow),
    new Config(32, 258, 258, 4096, dudu_slow),
];
var crcTable = makeTable();

function crc32(t, e, a, _) {
    var s = crcTable,
        n = _ + a;
    t ^= -1;
    for (var r = _; r < n; r++) t = (t >>> 8) ^ s[255 & (t ^ e[r])];
    return -1 ^ t;
}

var Z_BINARY = 0,
    Z_TEXT = 1;

function zero1(t) {
    for (var e = t.length; 0 <= --e;) t[e] = 0;
}

var L_CODES,
    STORED_BLOCK = 0,
    STATIC_TREES = 1,
    DYN_TREES = 2,
    Buf_size = ((D_CODES = 30), (HEAP_SIZE = 2 * (L_CODES = 286) + 1), 16),
    MAX_BL_BITS = 7,
    END_BLOCK = 256,
    REP_3_6 = 16,
    REPZ_3_10 = 17,
    REPZ_11_138 = 18,
    extra_lbits = [
        0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5,
        5, 5, 5, 0,
    ],
    extra_dbits = [
        0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10,
        11, 11, 12, 12, 13, 13,
    ],
    extra_blbits = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7],
    bl_order = [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15],
    DIST_CODE_LEN = 512,
    static_ltree = new Array(2 * (L_CODES + 2));
zero1(static_ltree);
var static_dtree = new Array(2 * D_CODES);
zero1(static_dtree);
var _dist_code = new Array(DIST_CODE_LEN);
zero1(_dist_code);
var _length_code = new Array(256);
zero1(_length_code);
var base_length = new Array(29);
zero1(base_length);
var static_l_desc,
    static_d_desc,
    static_bl_desc,
    base_dist = new Array(D_CODES);

function StaticTreeDesc(t, e, a, _, s) {
    (this.static_tree = t),
        (this.extra_bits = e),
        (this.extra_base = a),
        (this.elems = _),
        (this.max_length = s),
        (this.has_stree = t && t.length);
}

function TreeDesc(t, e) {
    (this.dyn_tree = t), (this.max_code = 0), (this.stat_desc = e);
}

function d_code(t) {
    return t < 256 ? _dist_code[t] : _dist_code[256 + (t >>> 7)];
}

function put_short(t, e) {
    (t.pending_buf[t.pending++] = 255 & e),
        (t.pending_buf[t.pending++] = (e >>> 8) & 255);
}

function send_bits(t, e, a) {
    t.bi_valid > Buf_size - a
        ? ((t.bi_buf |= (e << t.bi_valid) & 65535),
            put_short(t, t.bi_buf),
            (t.bi_buf = e >> (Buf_size - t.bi_valid)),
            (t.bi_valid += a - Buf_size))
        : ((t.bi_buf |= (e << t.bi_valid) & 65535), (t.bi_valid += a));
}

function send_code(t, e, a) {
    send_bits(t, a[2 * e], a[2 * e + 1]);
}

function bi_reverse(t, e) {
    for (var a = 0; (a |= 1 & t), (t >>>= 1), (a <<= 1), 0 < --e;) ;
    return a >>> 1;
}

function bi_flush(t) {
    16 === t.bi_valid
        ? (put_short(t, t.bi_buf), (t.bi_buf = 0), (t.bi_valid = 0))
        : 8 <= t.bi_valid &&
        ((t.pending_buf[t.pending++] = 255 & t.bi_buf),
            (t.bi_buf >>= 8),
            (t.bi_valid -= 8));
}

function gen_bitlen(t, e) {
    var a,
        _,
        s,
        n,
        r,
        i,
        l = e.dyn_tree,
        d = e.max_code,
        h = e.stat_desc.static_tree,
        o = e.stat_desc.has_stree,
        u = e.stat_desc.extra_bits,
        c = e.stat_desc.extra_base,
        f = e.stat_desc.max_length,
        b = 0;
    for (n = 0; n <= 15; n++) t.bl_count[n] = 0;
    for (
        l[2 * t.heap[t.heap_max] + 1] = 0, a = t.heap_max + 1;
        a < HEAP_SIZE;
        a++
    )
        f < (n = l[2 * l[2 * (_ = t.heap[a]) + 1] + 1] + 1) && ((n = f), b++),
            (l[2 * _ + 1] = n),
        d < _ ||
        (t.bl_count[n]++,
            (r = 0),
        c <= _ && (r = u[_ - c]),
            (i = l[2 * _]),
            (t.opt_len += i * (n + r)),
        o && (t.static_len += i * (h[2 * _ + 1] + r)));
    if (0 !== b) {
        do {
            for (n = f - 1; 0 === t.bl_count[n];) n--;
            t.bl_count[n]--, (t.bl_count[n + 1] += 2), t.bl_count[f]--, (b -= 2);
        } while (0 < b);
        for (n = f; 0 !== n; n--)
            for (_ = t.bl_count[n]; 0 !== _;)
                d < (s = t.heap[--a]) ||
                (l[2 * s + 1] !== n &&
                ((t.opt_len += (n - l[2 * s + 1]) * l[2 * s]), (l[2 * s + 1] = n)),
                    _--);
    }
}

function gen_codes(t, e, a) {
    var _,
        s,
        n = new Array(16),
        r = 0;
    for (_ = 1; _ <= 15; _++) n[_] = r = (r + a[_ - 1]) << 1;
    for (s = 0; s <= e; s++) {
        var i = t[2 * s + 1];
        0 !== i && (t[2 * s] = bi_reverse(n[i]++, i));
    }
}

function tr_static_init() {
    var t,
        e,
        a,
        _,
        s,
        n = new Array(16);
    for (_ = a = 0; _ < 28; _++)
        for (base_length[_] = a, t = 0; t < 1 << extra_lbits[_]; t++)
            _length_code[a++] = _;
    for (_length_code[a - 1] = _, _ = s = 0; _ < 16; _++)
        for (base_dist[_] = s, t = 0; t < 1 << extra_dbits[_]; t++)
            _dist_code[s++] = _;
    for (s >>= 7; _ < D_CODES; _++)
        for (base_dist[_] = s << 7, t = 0; t < 1 << (extra_dbits[_] - 7); t++)
            _dist_code[256 + s++] = _;
    for (e = 0; e <= 15; e++) n[e] = 0;
    for (t = 0; t <= 143;) (static_ltree[2 * t + 1] = 8), t++, n[8]++;
    for (; t <= 255;) (static_ltree[2 * t + 1] = 9), t++, n[9]++;
    for (; t <= 279;) (static_ltree[2 * t + 1] = 7), t++, n[7]++;
    for (; t <= 287;) (static_ltree[2 * t + 1] = 8), t++, n[8]++;
    for (gen_codes(static_ltree, L_CODES + 1, n), t = 0; t < D_CODES; t++)
        (static_dtree[2 * t + 1] = 5), (static_dtree[2 * t] = bi_reverse(t, 5));
    (static_l_desc = new StaticTreeDesc(
        static_ltree,
        extra_lbits,
        257,
        L_CODES,
        15
    )),
        (static_d_desc = new StaticTreeDesc(
            static_dtree,
            extra_dbits,
            0,
            D_CODES,
            15
        )),
        (static_bl_desc = new StaticTreeDesc(
            new Array(0),
            extra_blbits,
            0,
            19,
            MAX_BL_BITS
        ));
}

function init_block(t) {
    var e;
    for (e = 0; e < L_CODES; e++) t.dyn_ltree[2 * e] = 0;
    for (e = 0; e < D_CODES; e++) t.dyn_dtree[2 * e] = 0;
    for (e = 0; e < 19; e++) t.bl_tree[2 * e] = 0;
    (t.dyn_ltree[2 * END_BLOCK] = 1),
        (t.opt_len = t.static_len = 0),
        (t.last_lit = t.matches = 0);
}

function bi_windup(t) {
    8 < t.bi_valid
        ? put_short(t, t.bi_buf)
        : 0 < t.bi_valid && (t.pending_buf[t.pending++] = t.bi_buf),
        (t.bi_buf = 0),
        (t.bi_valid = 0);
}

function copy_block(t, e, a, _) {
    bi_windup(t),
    _ && (put_short(t, a), put_short(t, ~a)),
        utils.arraySet(t.pending_buf, t.window, e, a, t.pending),
        (t.pending += a);
}

function smaller(t, e, a, _) {
    var s = 2 * e,
        n = 2 * a;
    return t[s] < t[n] || (t[s] === t[n] && _[e] <= _[a]);
}

function pqdownheap(t, e, a) {
    for (
        var _ = t.heap[a], s = a << 1;
        s <= t.heap_len &&
        (s < t.heap_len && smaller(e, t.heap[s + 1], t.heap[s], t.depth) && s++,
            !smaller(e, _, t.heap[s], t.depth));
    )
        (t.heap[a] = t.heap[s]), (a = s), (s <<= 1);
    t.heap[a] = _;
}

function compress_block(t, e, a) {
    var _,
        s,
        n,
        r,
        i = 0;
    if (0 !== t.last_lit)
        for (
            ;
            (_ =
                (t.pending_buf[t.d_buf + 2 * i] << 8) |
                t.pending_buf[t.d_buf + 2 * i + 1]),
                (s = t.pending_buf[t.l_buf + i]),
                i++,
                0 === _
                    ? send_code(t, s, e)
                    : (send_code(t, (n = _length_code[s]) + 256 + 1, e),
                    0 !== (r = extra_lbits[n]) &&
                    send_bits(t, (s -= base_length[n]), r),
                        send_code(t, (n = d_code(--_)), a),
                    0 !== (r = extra_dbits[n]) && send_bits(t, (_ -= base_dist[n]), r)),
            i < t.last_lit;
        ) ;
    send_code(t, END_BLOCK, e);
}

function build_tree(t, e) {
    var a,
        _,
        s,
        n = e.dyn_tree,
        r = e.stat_desc.static_tree,
        i = e.stat_desc.has_stree,
        l = e.stat_desc.elems,
        d = -1;
    for (t.heap_len = 0, t.heap_max = HEAP_SIZE, a = 0; a < l; a++)
        0 !== n[2 * a]
            ? ((t.heap[++t.heap_len] = d = a), (t.depth[a] = 0))
            : (n[2 * a + 1] = 0);
    for (; t.heap_len < 2;)
        (n[2 * (s = t.heap[++t.heap_len] = d < 2 ? ++d : 0)] = 1),
            (t.depth[s] = 0),
            t.opt_len--,
        i && (t.static_len -= r[2 * s + 1]);
    for (e.max_code = d, a = t.heap_len >> 1; 1 <= a; a--) pqdownheap(t, n, a);
    for (
        s = l;
        (a = t.heap[1]),
            (t.heap[1] = t.heap[t.heap_len--]),
            pqdownheap(t, n, 1),
            (_ = t.heap[1]),
            (t.heap[--t.heap_max] = a),
            (t.heap[--t.heap_max] = _),
            (n[2 * s] = n[2 * a] + n[2 * _]),
            (t.depth[s] = (t.depth[a] >= t.depth[_] ? t.depth[a] : t.depth[_]) + 1),
            (n[2 * a + 1] = n[2 * _ + 1] = s),
            (t.heap[1] = s++),
            pqdownheap(t, n, 1),
        2 <= t.heap_len;
    ) ;
    (t.heap[--t.heap_max] = t.heap[1]),
        gen_bitlen(t, e),
        gen_codes(n, d, t.bl_count);
}

function scan_tree(t, e, a) {
    var _,
        s,
        n = -1,
        r = e[1],
        i = 0,
        l = 7,
        d = 4;
    for (
        0 === r && ((l = 138), (d = 3)), e[2 * (a + 1) + 1] = 65535, _ = 0;
        _ <= a;
        _++
    )
        (s = r),
            (r = e[2 * (_ + 1) + 1]),
        (++i < l && s === r) ||
        (i < d
            ? (t.bl_tree[2 * s] += i)
            : 0 !== s
                ? (s !== n && t.bl_tree[2 * s]++, t.bl_tree[2 * REP_3_6]++)
                : i <= 10
                    ? t.bl_tree[2 * REPZ_3_10]++
                    : t.bl_tree[2 * REPZ_11_138]++,
            (n = s),
            (d =
                (i = 0) === r
                    ? ((l = 138), 3)
                    : s === r
                        ? ((l = 6), 3)
                        : ((l = 7), 4)));
}

function send_tree(t, e, a) {
    var _,
        s,
        n = -1,
        r = e[1],
        i = 0,
        l = 7,
        d = 4;
    for (0 === r && ((l = 138), (d = 3)), _ = 0; _ <= a; _++)
        if (((s = r), (r = e[2 * (_ + 1) + 1]), !(++i < l && s === r))) {
            if (i < d) for (; send_code(t, s, t.bl_tree), 0 != --i;) ;
            else
                0 !== s
                    ? (s !== n && (send_code(t, s, t.bl_tree), i--),
                        send_code(t, REP_3_6, t.bl_tree),
                        send_bits(t, i - 3, 2))
                    : i <= 10
                        ? (send_code(t, REPZ_3_10, t.bl_tree), send_bits(t, i - 3, 3))
                        : (send_code(t, REPZ_11_138, t.bl_tree), send_bits(t, i - 11, 7));
            (n = s),
                (d =
                    (i = 0) === r
                        ? ((l = 138), 3)
                        : s === r
                            ? ((l = 6), 3)
                            : ((l = 7), 4));
        }
}

function build_bl_tree(t) {
    var e;
    for (
        scan_tree(t, t.dyn_ltree, t.l_desc.max_code),
            scan_tree(t, t.dyn_dtree, t.d_desc.max_code),
            build_tree(t, t.bl_desc),
            e = 18;
        3 <= e && 0 === t.bl_tree[2 * bl_order[e] + 1];
        e--
    ) ;
    return (t.opt_len += 3 * (e + 1) + 5 + 5 + 4), e;
}

function send_all_trees(t, e, a, _) {
    var s;
    for (
        send_bits(t, e - 257, 5),
            send_bits(t, a - 1, 5),
            send_bits(t, _ - 4, 4),
            s = 0;
        s < _;
        s++
    )
        send_bits(t, t.bl_tree[2 * bl_order[s] + 1], 3);
    send_tree(t, t.dyn_ltree, e - 1), send_tree(t, t.dyn_dtree, a - 1);
}

function detect_data_type(t) {
    var e,
        a = 4093624447;
    for (e = 0; e <= 31; e++, a >>>= 1)
        if (1 & a && 0 !== t.dyn_ltree[2 * e]) return Z_BINARY;
    if (0 !== t.dyn_ltree[18] || 0 !== t.dyn_ltree[20] || 0 !== t.dyn_ltree[26])
        return Z_TEXT;
    for (e = 32; e < 256; e++) if (0 !== t.dyn_ltree[2 * e]) return Z_TEXT;
    return Z_BINARY;
}

zero1(base_dist);
var static_init_done = !1;

function _tr_init(t) {
    static_init_done || (tr_static_init(), (static_init_done = !0)),
        (t.l_desc = new TreeDesc(t.dyn_ltree, static_l_desc)),
        (t.d_desc = new TreeDesc(t.dyn_dtree, static_d_desc)),
        (t.bl_desc = new TreeDesc(t.bl_tree, static_bl_desc)),
        (t.bi_buf = 0),
        (t.bi_valid = 0),
        init_block(t);
}

function _tr_stored_block(t, e, a, _) {
    send_bits(t, (STORED_BLOCK << 1) + (_ ? 1 : 0), 3), copy_block(t, e, a, !0);
}

function _tr_align(t) {
    send_bits(t, STATIC_TREES << 1, 3),
        send_code(t, END_BLOCK, static_ltree),
        bi_flush(t);
}

function _tr_flush_block(t, e, a, _) {
    var s,
        n,
        r = 0;
    0 < t.level
        ? (2 === t.strm.data_type && (t.strm.data_type = detect_data_type(t)),
            build_tree(t, t.l_desc),
            build_tree(t, t.d_desc),
            (r = build_bl_tree(t)),
            (s = (t.opt_len + 3 + 7) >>> 3),
        (n = (t.static_len + 3 + 7) >>> 3) <= s && (s = n))
        : (s = n = a + 5),
        a + 4 <= s && -1 !== e
            ? _tr_stored_block(t, e, a, _)
            : 4 === t.strategy || n === s
                ? (send_bits(t, (STATIC_TREES << 1) + (_ ? 1 : 0), 3),
                    compress_block(t, static_ltree, static_dtree))
                : (send_bits(t, (DYN_TREES << 1) + (_ ? 1 : 0), 3),
                    send_all_trees(t, t.l_desc.max_code + 1, t.d_desc.max_code + 1, r + 1),
                    compress_block(t, t.dyn_ltree, t.dyn_dtree)),
        init_block(t),
    _ && bi_windup(t);
}

function _tr_tally(t, e, a) {
    return (
        (t.pending_buf[t.d_buf + 2 * t.last_lit] = (e >>> 8) & 255),
            (t.pending_buf[t.d_buf + 2 * t.last_lit + 1] = 255 & e),
            (t.pending_buf[t.l_buf + t.last_lit] = 255 & a),
            t.last_lit++,
            0 === e
                ? t.dyn_ltree[2 * a]++
                : (t.matches++,
                    e--,
                    t.dyn_ltree[2 * (_length_code[a] + 256 + 1)]++,
                    t.dyn_dtree[2 * d_code(e)]++),
        t.last_lit === t.lit_bufsize - 1
    );
}

(exports.duduInit = duduInit),
    (exports.duduInit2 = duduInit2),
    (exports.duduReset = duduReset),
    (exports.duduResetKeep = duduResetKeep),
    (exports.duduSetHeader = duduSetHeader),
    (exports.dudu = dudu),
    (exports.duduEnd = duduEnd),
    (exports.duduSetDictionary = duduSetDictionary),
    (exports.duduInfo = "pako dudu (from Nodeca project)");
