"use strict";
var TYPED_OK =
    "undefined" != typeof Uint8Array &&
    "undefined" != typeof Uint16Array &&
    "undefined" != typeof Int32Array;

function _has(r, t) {
    return Object.prototype.hasOwnProperty.call(r, t);
}

(exports.assign = function (r) {
    for (var t = Array.prototype.slice.call(arguments, 1); t.length;) {
        var e = t.shift();
        if (e) {
            if ("object" != typeof e) throw new TypeError(e + "must be non-object");
            for (var n in e) _has(e, n) && (r[n] = e[n]);
        }
    }
    return r;
}),
    (exports.shrinkBuf = function (r, t) {
        return r.length === t
            ? r
            : r.subarray
                ? r.subarray(0, t)
                : ((r.length = t), r);
    });
var fnTyped = {
        arraySet: function (r, t, e, n, a) {
            if (t.subarray && r.subarray) r.set(t.subarray(e, e + n), a);
            else for (var o = 0; o < n; o++) r[a + o] = t[e + o];
        },
        flattenChunks: function (r) {
            var t, e, n, a, o, s;
            for (t = n = 0, e = r.length; t < e; t++) n += r[t].length;
            for (s = new Uint8Array(n), t = a = 0, e = r.length; t < e; t++)
                (o = r[t]), s.set(o, a), (a += o.length);
            return s;
        },
    },
    fnUntyped = {
        arraySet: function (r, t, e, n, a) {
            for (var o = 0; o < n; o++) r[a + o] = t[e + o];
        },
        flattenChunks: function (r) {
            return [].concat.apply([], r);
        },
    };
(exports.setTyped = function (r) {
    r
        ? ((exports.Buf8 = Uint8Array),
            (exports.Buf16 = Uint16Array),
            (exports.Buf32 = Int32Array),
            exports.assign(exports, fnTyped))
        : ((exports.Buf8 = Array),
            (exports.Buf16 = Array),
            (exports.Buf32 = Array),
            exports.assign(exports, fnUntyped));
}),
    exports.setTyped(TYPED_OK);
