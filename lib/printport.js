let myInterface = require("./interface.js");
const {processImageData} = require("./image_processing/image-processing");
const {
    learnLabelGap,
    gePrinterInfor,
    getPrinterBtname,
    getPrinterMac
} = require("./printer_commands/printer-commands");

/**
 * Adjust the position of the printing paper
 *
 * @param mode   Adjustment mode
 *                 0x00: Feed forward (unit: pixels)
 *                 0x01: Feed forward (unit: mm)
 *                 0x10: Reverse feed (unit: pixels)
 *                 0x11: Reverse feed (unit: mm)
 * @param distance  Distance to move the paper
 */
function adjustPosition(mode, distance) {
    return myInterface.adjustPosition(mode, distance);
}
/**
 * Print positioning
 */
function printerPosition() {
    return myInterface.printerPosition();
}

/**
 * Paper feed command
 * @param linedots Number of lines to feed the paper
 */
function printLinedots(linedots) {
    return myInterface.printLinedots(linedots);
}
/**
 * Query label height. You must first position a few labels; it is recommended to position 3 labels. Wait for three "OK" responses before calling this interface to obtain label height. Otherwise, the height might be inaccurate.
 */
function getLabelHeight() {
    return myInterface.getLabelHeight();
}

/**
 * Set printer density (0-2)
 * 0: Low density
 * 1: Medium density
 * 2: High density
 */
function setDensity(level) {
    return myInterface.setDensity(level);
}

/**
 * Set Bluetooth transfer type
 * @return Data sent to the printer
 */
function setBTType() {
    return myInterface.setBTType();
}
/**
 * Start print job marker
 */
function startPrintjob() {
    return myInterface.startPrintjob();
}
/**
 * End print job marker
 */
function stopPrintjob() {
    return myInterface.stopPrintjob();
}

/**
 * Set paper type
 *
 * @param model Setting mode
 *              0x01: Set paper type. Printer returns "OK\r\n" on success, "ER\r\n" on failure.
 *              0x02: Set paper type without a return value
 * @param type  Paper type
 *              0x10: Continuous paper
 *              0x20: Gap paper
 *              0x30: Black mark paper
 */

function setPaperType(model, type) {
    return myInterface.setPaperType(model, type);
}
/**
 * Automatically adjust the position of the printing paper
 *
 * @param model Adjustment mode
 *              0x50: Feed forward
 *              0x51: Reverse feed
 */
function adjustPositionAuto(model) {
    return myInterface.adjustPositionAuto(model);
}
/**
 * Query printer status
 * 0: Printer is normal
 * Others (determine printer status based on "bits")
 * Bit 0: 1 - Printing in progress
 * Bit 1: 1 - Paper compartment open
 * Bit 2: 1 - Out of paper
 * Bit 3: 1 - Low battery voltage
 * Bit 4: 1 - Print head overheating
 * Bit 5: Reserved (default 0)
 * Bit 6: Reserved (default 0)
 * Bit 7: Reserved (default 0)
 */
function getPrinterStatus() {
    return myInterface.getPrinterStatus();
}

/**
 * Query printer battery voltage
 * Voltage (returned as a percentage)
 */
function getPrinterBatteryVol() {
    return myInterface.getPrinterBatteryVol();
}
/**
 * Query printer firmware version
 *
 */
function getPrinterVersion() {
    return myInterface.getPrinterVersion();
}
/**
 * Query printer SN
 */
function getPrinterSN() {
    return myInterface.getPrinterSN();
}
function setShutTime(time) {
    return myInterface.setShutTime(time);
}
function getShutTime() {
    return myInterface.getShutTime();
}

// Merge byte arrays
function merge(data1, data2) {
    var dataView1 = new DataView(data1);
    var dataView2 = new DataView(data2);
    var buffer = new ArrayBuffer(dataView1.byteLength + dataView2.byteLength);
    var dataView = new DataView(buffer);
    for (var i = 0; i < dataView.byteLength; i++) {
        if (i < dataView1.byteLength) {
            dataView.setUint8(i, dataView1.getInt8(i));
        } else {
            dataView.setUint8(i, dataView2.getInt8(i - dataView1.byteLength));
        }
    }
    return buffer;
}

module.exports = {
    processImageData,
    startPrintjob,
    stopPrintjob,
    adjustPositionAuto,
    adjustPosition,
    setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
};
