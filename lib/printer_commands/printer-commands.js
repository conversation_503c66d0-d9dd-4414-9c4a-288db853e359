"use strict";

const {<PERSON><PERSON><PERSON>} = require("buffer");

/**
 * Start a print job.
 *
 * @returns {Buffer} Command buffer to start a print job.
 */
function startPrintjob() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 254);
    e.setUint8(3, 1);
    return Buffer.from(t); // Converts ArrayBuffer to <PERSON>uffer
}

/**
 * Stop a print job.
 *
 * @returns {Buffer} Command buffer to stop a print job.
 */
function stopPrintjob() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 254);
    e.setUint8(3, 69);
    return Buffer.from(t); // Converts ArrayBuffer to Buffer
}

/**
 * Automatically adjust the position of the printing paper.
 *
 * @param {number} mode - Adjustment mode (e.g., 0x50 for feed forward, 0x51 for reverse feed).
 * @returns {Buffer} Command buffer to adjust position automatically.
 */
function adjustPositionAuto(mode) {
    var e = new ArrayBuffer(3);
    var n = new DataView(e);
    n.setUint8(0, 31);
    n.setUint8(1, 17);
    n.setUint8(2, mode);
    return Buffer.from(e); // Converts ArrayBuffer to Buffer
}

/**
 * Adjust the position of the printing paper
 *
 * @param {number} mode - Adjustment mode
 *                 0x00: Feed forward (unit: pixels)
 *                 0x01: Feed forward (unit: mm)
 *                 0x10: Reverse feed (unit: pixels)
 *                 0x11: Reverse feed (unit: mm)
 * @param {number} distance  Distance to move the paper
 */
function adjustPosition(t, e) {
    var n = new ArrayBuffer(5);
    var i = new DataView(n);
    i.setUint8(0, 31);
    i.setUint8(1, 17);
    i.setUint8(2, t);
    i.setUint8(3, e / 256);
    i.setUint8(4, e % 256);
    return Buffer.from(n);
}

/**
 * Set the Bluetooth transfer type.
 *
 * @returns {Buffer} Command buffer to set the Bluetooth transfer type.
 */
function setBTType() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 31);
    e.setUint8(1, 178);
    e.setUint8(2, 17);
    return Buffer.from(t);
}

/**
 * Learn the label gap for the printer.
 *
 * @returns {Buffer} Command buffer to learn the label gap.
 */
function learnLabelGap() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 3);
    return Buffer.from(t);
}

/**
 * Retrieve the printer position.
 *
 * @returns {Buffer} Command buffer to get the printer position.
 */
function printerPosition() {
    var t = new ArrayBuffer(2);
    var e = new DataView(t);
    e.setUint8(0, 29);
    e.setUint8(1, 12);
    return Buffer.from(t);
}

/**
 * Print a specified number of line dots.
 *
 * @param {number} linedots - Number of line dots to feed.
 * @returns {Buffer} Command buffer to print line dots.
 */
function printLinedots(linedots) {
    var e = new ArrayBuffer(3);
    var n = new DataView(e);
    n.setUint8(0, 27);
    n.setUint8(1, 74);
    n.setUint8(2, linedots);
    return Buffer.from(e);
}

/**
 * Query label height. You must first position a few labels; it is recommended to position 3 labels. Wait for three "OK" responses before calling this interface to obtain label height. Otherwise, the height might be inaccurate.
 *
 * @returns {Buffer} Command buffer to get the label height.
 */
function getLabelHeight() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 80);
    e.setUint8(3, 242);
    return Buffer.from(t);
}

/**
 * Set the printer density level.
 *
 * @param {number} level - Density level (0: low, 1: medium, 2: high).
 * @returns {Buffer} Command buffer to set printer density.
 */
function setDensity(level) {
    var e = new ArrayBuffer(5);
    var n = new DataView(e);
    n.setUint8(0, 16);
    n.setUint8(1, 255);
    n.setUint8(2, 16);
    n.setUint8(3, 0);
    n.setUint8(4, level);
    return Buffer.from(e);
}

/**
 * Get information about the printer.
 *
 * @returns {Buffer} Command buffer to get printer information.
 */
function gePrinterInfor() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 112);
    return Buffer.from(t);
}

/**
 * Query printer status.
 *
 * @returns {Buffer} Command buffer to get printer status.
 * Status bits:
 *   - Bit 0: 1 - Printing in progress.
 *   - Bit 1: 1 - Paper compartment open.
 *   - Bit 2: 1 - Out of paper.
 *   - Bit 3: 1 - Low battery voltage.
 *   - Bit 4: 1 - Print head overheating.
 *   - Bit 5: Reserved (default 0).
 *   - Bit 6: Reserved (default 0).
 *   - Bit 7: Reserved (default 0).
 */
function getPrinterStatus() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 64);
    return Buffer.from(t);
}

/**
 * Query the printer's battery voltage.
 *
 * @returns {Buffer} Command buffer to get printer battery voltage.
 */
function getPrinterBatteryVol() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 80);
    e.setUint8(3, 241);
    return Buffer.from(t);
}

/**
 * Query the printer's Bluetooth name.
 *
 * @returns {Buffer} Command buffer to get the printer's Bluetooth name.
 */
function getPrinterBtname() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 48);
    e.setUint8(3, 17);
    return Buffer.from(t);
}

/**
 * Query the printer's MAC address.
 *
 * @returns {Buffer} Command buffer to get the printer's MAC address.
 */
function getPrinterMac() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 48);
    e.setUint8(3, 18);
    return Buffer.from(t);
}

/**
 * Set paper type.
 *
 * @param {number} model - Setting mode:
 *                         0x01: Set paper type. Printer returns "OK\r\n" on success, "ER\r\n" on failure.
 *                         0x02: Set paper type without a return value.
 * @param {number} type - Paper type:
 *                        0x10: Continuous paper.
 *                        0x20: Gap paper.
 *                        0x30: Black mark paper.
 * @returns {Buffer} Command buffer to set the paper type.
 */
function setPaperType(model, type) {
    var n = new ArrayBuffer(4);
    var i = new DataView(n);
    i.setUint8(0, 31);
    i.setUint8(1, 128);
    i.setUint8(2, model);
    i.setUint8(3, type);
    return Buffer.from(n);
}

function getPrinterVersion() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 32);
    e.setUint8(3, 241);
    return Buffer.from(t);
}

function getPrinterSN() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 32);
    e.setUint8(3, 242);
    return Buffer.from(t);
}

function setShutTime(t) {
    var e = new ArrayBuffer(5);
    var n = new DataView(e);
    n.setUint8(0, 16);
    n.setUint8(1, 255);
    n.setUint8(2, 18);
    n.setUint8(3, t / 256);
    n.setUint8(4, t % 256);
    return Buffer.from(e);
}

function getShutTime() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 19);
    return Buffer.from(t);
}

module.exports = {
    startPrintjob,
    stopPrintjob,
    adjustPositionAuto,
    adjustPosition,
    setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
};
