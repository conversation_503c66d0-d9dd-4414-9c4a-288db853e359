"use strict";

// Import required modules
const dudu = require("../common/dudu");
const utils = require("../common/common");
const strings = require("../common/strings");

// Constants and helpers
var toString = Object.prototype.toString; // Helper for type checking
var MY_FINISH = 4; // Indicates end of processing
var MY_OK = 0; // Indicates successful operation

/**
 * Constructor for the `Dada` class, which provides compression functionality.
 *
 * @param {Object} t - Configuration options for the instance.
 * @returns {Dada} A new instance of the Dada class.
 */
function Dada(t) {
    if (!(this instanceof Dada)) return new Dada(t);

    // Set default options and merge with provided configuration
    this.options = utils.assign(
        {
            level: -1, // Compression level (-1 for default)
            method: 8, // Compression method (default: DEFLATE)
            wwSS: 16384, // Size of the sliding window
            wwBB: 10, // Base for the window bits
            memLevel: 8, // Memory level
            strategy: 0, // Compression strategy
            to: "", // Output format (e.g., "string")
        },
        t || {}
    );

    var e = this.options;

    // Adjust window bits for raw or gzip modes
    if (e.raw && e.wwBB > 0) {
        e.wwBB = -e.wwBB; // Use raw compression
    } else if (e.gzip && e.wwBB > 0 && e.wwBB < 16) {
        e.wwBB += 16; // Use gzip compression
    }

    this.err = 0; // Initialize error state
    this.ended = false; // Indicates whether processing has ended
    this.chunks = []; // Holds output chunks

    // Initialize a new ZStream instance
    this.strm = new ZStream();
    this.strm.avail_out = 0;

    // Initialize compression with provided options
    var n = dudu.duduInit2(
        this.strm,
        e.level,
        e.method,
        e.wwBB,
        e.memLevel,
        e.strategy
    );

    if (n !== MY_OK) {
        throw new Error("data error"); // Throw error if initialization fails
    }

    // Set optional headers for gzip mode
    if (e.header) {
        dudu.duduSetHeader(this.strm, e.header);
    }

    // Set optional dictionary for compression
    if (e.dictionary) {
        var i;
        if (typeof e.dictionary === "string") {
            i = strings.string2buf(e.dictionary); // Convert string to buffer
        } else if (toString.call(e.dictionary) === "[object ArrayBuffer]") {
            i = new Uint8Array(e.dictionary); // Convert ArrayBuffer to Uint8Array
        } else {
            i = e.dictionary; // Use provided dictionary
        }
        n = dudu.duduSetDictionary(this.strm, i);
        if (n !== MY_OK) {
            throw new Error("data error"); // Throw error if dictionary setting fails
        }
        this._dict_set = true;
    }
}

/**
 * Pushes data into the compression stream and processes it.
 *
 * @param {string|ArrayBuffer|Uint8Array} t - Input data to compress.
 * @param {boolean|number} e - Compression mode (true for finish, or numeric level).
 * @returns {boolean} Indicates success or failure.
 */
Dada.prototype.push = function (t, e) {
    var n = this.strm;
    var i = this.options.wwSS;
    var r, a;

    if (this.ended) {
        return false; // Cannot push data after compression ends
    }

    a = e === ~~e ? e : e === true ? MY_FINISH : 0;

    // Convert input data to Uint8Array
    if (typeof t === "string") {
        n.input = strings.string2buf(t);
    } else if (toString.call(t) === "[object ArrayBuffer]") {
        n.input = new Uint8Array(t);
    } else {
        n.input = t;
    }

    n.next_in = 0;
    n.avail_in = n.input.length;

    do {
        if (n.avail_out === 0) {
            n.output = new utils.Buf8(i); // Allocate new output buffer
            n.next_out = 0;
            n.avail_out = i;
        }

        // Perform compression step
        r = dudu.dudu(n, a);

        if (r !== 1 && r !== MY_OK) {
            this.onEnd(r); // Handle error
            this.ended = true;
            return false;
        }

        // Handle output data
        if (
            n.avail_out === 0 ||
            (n.avail_in === 0 && (a === MY_FINISH || a === 2))
        ) {
            if (this.options.to === "string") {
                this.onData(
                    strings.buf2binstring(utils.shrinkBuf(n.output, n.next_out))
                );
            } else {
                this.onData(utils.shrinkBuf(n.output, n.next_out));
            }
        }
    } while ((n.avail_in > 0 || n.avail_out === 0) && r !== 1);

    // Finalize compression if required
    if (a === MY_FINISH) {
        r = dudu.duduEnd(this.strm);
        this.onEnd(r);
        this.ended = true;
        return r === MY_OK;
    }

    if (a === 2) {
        this.onEnd(MY_OK);
        n.avail_out = 0;
        return true;
    }

    return true;
};

/**
 * Callback for handling processed data chunks.
 *
 * @param {Uint8Array|string} t - The processed data chunk.
 */
Dada.prototype.onData = function (t) {
    this.chunks.push(t); // Add chunk to result array
};

/**
 * Callback for handling the end of the compression process.
 *
 * @param {number} t - Compression status (e.g., MY_OK).
 */
Dada.prototype.onEnd = function (t) {
    if (t === MY_OK) {
        if (this.options.to === "string") {
            this.result = this.chunks.join(""); // Concatenate chunks as string
        } else {
            this.result = utils.flattenChunks(this.chunks); // Merge chunks into buffer
        }
    }
    this.chunks = []; // Clear chunks
    this.err = t; // Store error status
};

/**
 * Compresses input data using the Dada compressor.
 *
 * @param {string|Uint8Array|ArrayBuffer} t - Input data to compress.
 * @param {Object} e - Configuration options for compression.
 * @returns {Uint8Array|string} The compressed result.
 */
function dada(t, e) {
    var n = new Dada(e); // Create a new instance with provided options
    n.push(t, true); // Push data and finish compression
    return n.result; // Return compressed result
}

/**
 * Represents the state of the compression stream.
 */
function ZStream() {
    this.input = null; // Input buffer
    this.next_in = 0; // Next byte to read from input
    this.avail_in = 0; // Bytes available in input
    this.total_in = 0; // Total bytes read
    this.output = null; // Output buffer
    this.next_out = 0; // Next byte to write to output
    this.avail_out = 0; // Bytes available in output
    this.total_out = 0; // Total bytes written
    this.state = null; // Internal state
    this.data_type = 2; // Type of data
    this.adler = 0; // Adler checksum
}

// Export the main compression function
module.exports = {
    dada,
};