"use strict";
var currentDevName = "defaulName";
var currentConnectionState = "已连接";
var recvTotalBytes = "0";
var recvCrc32 = "0";
var sendTotalBytesCounter = 0;
var sendTotalBytes = "0";
var sendCrc32 = "0";
var connectedDeviceId = "invalid";
var serviceId = "invalid";
var writeCharacteristicId = "invalid";
var notifyCharacteristicId = "invalid";
var notifyCharacteristicId1 = "invalid";
var debug = false;
var blueSize = 200;
var credit = 0;
var bmpdata = "";
let internal = "";
var dudu = require("../common/dudu");
var utils = require("../common/common");
var strings = require("../common/strings");
var toString = Object.prototype.toString;
var MY_FINISH = 4;
var MY_OK = 0;

function Dada(t) {
    if (!(this instanceof Dada)) return new Dada(t);
    this.options = utils.assign({
        level: -1,
        method: 8,
        wwSS: 16384,
        wwBB: 10,
        memLevel: 8,
        strategy: 0,
        to: ""
    }, t || {});
    var e = this.options;
    if (e.raw && e.wwBB > 0) {
        e.wwBB = -e.wwBB
    } else if (e.gzip && e.wwBB > 0 && e.wwBB < 16) {
        e.wwBB += 16
    }
    this.err = 0;
    this.ended = false;
    this.chunks = [];
    this.strm = new ZStream;
    this.strm.avail_out = 0;
    var n = dudu.duduInit2(this.strm, e.level, e.method, e.wwBB, e.memLevel, e.strategy);
    if (n !== MY_OK) {
        throw new Error("data error")
    }
    if (e.header) {
        dudu.duduSetHeader(this.strm, e.header)
    }
    if (e.dictionary) {
        var i;
        if (typeof e.dictionary === "string") {
            i = strings.string2buf(e.dictionary)
        } else if (toString.call(e.dictionary) === "[object ArrayBuffer]") {
            i = new Uint8Array(e.dictionary)
        } else {
            i = e.dictionary
        }
        n = dudu.duduSetDictionary(this.strm, i);
        if (n !== MY_OK) {
            throw new Error("data error")
        }
        this._dict_set = true
    }
}

Dada.prototype.push = function (t, e) {
    var n = this.strm;
    var i = this.options.wwSS;
    var r, a;
    if (this.ended) {
        return false
    }
    a = e === ~~e ? e : e === true ? MY_FINISH : 0;
    if (typeof t === "string") {
        n.input = strings.string2buf(t)
    } else if (toString.call(t) === "[object ArrayBuffer]") {
        n.input = new Uint8Array(t)
    } else {
        n.input = t
    }
    n.next_in = 0;
    n.avail_in = n.input.length;
    do {
        if (n.avail_out === 0) {
            n.output = new utils.Buf8(i);
            n.next_out = 0;
            n.avail_out = i
        }
        r = dudu.dudu(n, a);
        if (r !== 1 && r !== MY_OK) {
            this.onEnd(r);
            this.ended = true;
            return false
        }
        if (n.avail_out === 0 || n.avail_in === 0 && (a === MY_FINISH || a === 2)) {
            if (this.options.to === "string") {
                this.onData(strings.buf2binstring(utils.shrinkBuf(n.output, n.next_out)))
            } else {
                this.onData(utils.shrinkBuf(n.output, n.next_out))
            }
        }
    } while ((n.avail_in > 0 || n.avail_out === 0) && r !== 1);
    if (a === MY_FINISH) {
        r = dudu.duduEnd(this.strm);
        this.onEnd(r);
        this.ended = true;
        return r === MY_OK
    }
    if (a === 2) {
        this.onEnd(MY_OK);
        n.avail_out = 0;
        return true
    }
    return true
};
Dada.prototype.onData = function (t) {
    this.chunks.push(t)
};
Dada.prototype.onEnd = function (t) {
    if (t === MY_OK) {
        if (this.options.to === "string") {
            this.result = this.chunks.join("")
        } else {
            this.result = utils.flattenChunks(this.chunks)
        }
    }
    this.chunks = [];
    this.err = t
};

function dada(t, e) {
    var n = new Dada(e);
    n.push(t, true);
    return n.result
}

function connect(n, i, r) {
    if (n.name.indexOf("P50") != -1 || n.name.indexOf("P80") != -1) {
        wx.createBLEConnection({
            deviceId: n.devId, success: function (t) {
                const e = 100;
                wx.setBLEMTU({
                    deviceId: n.devId, mtu: e, success: t => {
                        if (debug) console.log("setBLEMTU success>>", t)
                    }, fail: t => {
                        if (debug) console.log("setBLEMTU fail>>", t)
                    }
                });
                connectedDeviceId = n.devId;
                getBLEDeviceServices(i, r)
            }, fail: function (t) {
                if (r) r(t);
                if (debug) console.log(t)
            }
        })
    }
}

function getBLEDeviceServices(i, n) {
    wx.getBLEDeviceServices({
        deviceId: connectedDeviceId, success: function (e) {
            for (let t = 0; t < e.services.length; t++) {
                if (debug) console.log(e.services[t]);
                if (e.services[t].uuid.indexOf("0000FF00-0000-1000-8000-00805F9B34FB") != -1) {
                    serviceId = e.services[t].uuid
                }
            }
            wx.getBLEDeviceCharacteristics({
                deviceId: connectedDeviceId, serviceId: serviceId, success: function (e) {
                    var n = 0;
                    for (let t = 0; t < e.characteristics.length; t++) {
                        if (debug) console.log(e.characteristics[t]);
                        if (e.characteristics[t].uuid.indexOf("0000FF02-0000-1000-8000-00805F9B34FB") != -1) {
                            n++;
                            writeCharacteristicId = e.characteristics[t].uuid;
                            if (n == 3) {
                                if (i) i({msg: "成功", deviceId: connectedDeviceId})
                            }
                            if (debug) console.log(e.characteristics[t].properties.read + " and " + e.characteristics[t].properties.write)
                        }
                        if (e.characteristics[t].uuid.indexOf("0000FF03-0000-1000-8000-00805F9B34FB") != -1) {
                            notifyCharacteristicId = e.characteristics[t].uuid;
                            wx.notifyBLECharacteristicValueChanged({
                                state: true,
                                deviceId: connectedDeviceId,
                                serviceId: serviceId,
                                characteristicId: notifyCharacteristicId,
                                success: function (t) {
                                    n++;
                                    if (n == 3) {
                                        if (i) i({msg: "成功", deviceId: connectedDeviceId})
                                    }
                                    if (debug) console.log("notifyBLECharacteristicValueChanged success", t.errMsg)
                                }
                            })
                        }
                        if (e.characteristics[t].uuid.indexOf("0000FF01-0000-1000-8000-00805F9B34FB") != -1) {
                            notifyCharacteristicId1 = e.characteristics[t].uuid;
                            wx.notifyBLECharacteristicValueChanged({
                                state: true,
                                deviceId: connectedDeviceId,
                                serviceId: serviceId,
                                characteristicId: notifyCharacteristicId1,
                                success: function (t) {
                                    n++;
                                    if (n == 3) {
                                        if (i) i({msg: "成功", deviceId: connectedDeviceId})
                                    }
                                    if (debug) console.log("notifyBLECharacteristicValueChanged success", t.errMsg)
                                }
                            })
                        }
                    }
                }, fail: function (t) {
                    if (debug) console.log(t);
                    if (n) n({msg: "获取服务失败"})
                }
            })
        }
    })
}

function onBLECharacteristicValueChange(n) {
    wx.onBLECharacteristicValueChange(function (t) {
        if (debug) console.log("onBLECharacteristicValueChange", t);
        if (t.characteristicId == "0000FF03-0000-1000-8000-00805F9B34FB") {
            var e = new Uint8Array(t.value);
            if (e[0] == 1) {
                if (e[1] > 0) {
                    credit = credit + e[1]
                }
            }
            if (e[0] == 2) {
                blueSize = e[1] - 10
            }
        } else {
            n(t)
        }
    })
}

function onBLEConnectionStateChanged(e) {
    wx.onBLEConnectionStateChanged(function (t) {
        e(t);
        if (debug) console.log(`device ${t.deviceId} state has changed, connected: ${t.connected}`)
    })
}

function onBluetoothAdapterStateChange(t) {
    wx.onBluetoothAdapterStateChange(function (t) {
        if (debug) console.log(`adapterState changed, now is`, t)
    })
}

function disconnect() {
    clearInterval(internal);
    wx.closeBLEConnection({
        deviceId: connectedDeviceId, success: function (t) {
            connectedDeviceId = "";
            serviceId = "";
            writeCharacteristicId = "";
            notifyCharacteristicId = "";
            notifyCharacteristicId1 = ""
        }
    })
}

function sendDatabypac() {
    var t = bmpdata.byteLength;
    if (debug) console.log("剩余数据长度", t);
    var e = null;
    var n = blueSize;
    if (t > n) {
        e = bmpdata.slice(0, n)
    } else {
        e = bmpdata.slice(0, t)
    }
    if (e) {
        wx.writeBLECharacteristicValue({
            deviceId: connectedDeviceId,
            serviceId: serviceId,
            characteristicId: writeCharacteristicId,
            value: e,
            success: function (t) {
                if (debug) console.log("数据发送成功", t.errMsg)
            },
            fail: function (t) {
                if (debug) console.log(t)
            }
        });
        credit = credit - 1, bmpdata = bmpdata.slice(e.byteLength, t)
    }
}

function sendData(t, e) {
    bmpdata = t;
    internal = setInterval(function () {
        if (bmpdata.byteLength > 0) {
            if (debug) console.log("credit " + credit);
            if (credit > 0) {
                sendDatabypac()
            }
        } else {
            clearInterval(internal);
            if (e) e()
        }
    }, 1)
}

function setBTType() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 31);
    e.setUint8(1, 178);
    e.setUint8(2, 17);
    return t
}

function learnLabelGap() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 3);
    return t
}

function printerPosition() {
    var t = new ArrayBuffer(2);
    var e = new DataView(t);
    e.setUint8(0, 29);
    e.setUint8(1, 12);
    return t
}

function printLinedots(t) {
    var e = new ArrayBuffer(3);
    var n = new DataView(e);
    n.setUint8(0, 27);
    n.setUint8(1, 74);
    n.setUint8(2, t);
    return e
}

function getLabelHeight() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 80);
    e.setUint8(3, 242);
    return t
}

function startPrintjob() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 254);
    e.setUint8(3, 1);
    return t
}

function stopPrintjob() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 254);
    e.setUint8(3, 69);
    return t
}

function setDensity(t) {
    var e = new ArrayBuffer(5);
    var n = new DataView(e);
    n.setUint8(0, 16);
    n.setUint8(1, 255);
    n.setUint8(2, 16);
    n.setUint8(3, 0);
    n.setUint8(4, t);
    return e
}

function gePrinterInfor() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 112);
    return t
}

function getPrinterStatus() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 64);
    return t
}

function getPrinterBatteryVol() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 80);
    e.setUint8(3, 241);
    return t
}

function getPrinterBtname() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 48);
    e.setUint8(3, 17);
    return t
}

function getPrinterMac() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 48);
    e.setUint8(3, 18);
    return t
}

function getBtVersion() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 48);
    e.setUint8(3, 16);
    return t
}

function getPrinterVersion() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 32);
    e.setUint8(3, 241);
    return t
}

function getPrinterSN() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 32);
    e.setUint8(3, 242);
    return t
}

function adjustPosition(t, e) {
    var n = new ArrayBuffer(5);
    var i = new DataView(n);
    i.setUint8(0, 31);
    i.setUint8(1, 17);
    i.setUint8(2, t);
    i.setUint8(3, e / 256);
    i.setUint8(4, e % 256);
    return n
}

function getPrinterModel() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 32);
    e.setUint8(3, 240);
    return t
}

function setShutTime(t) {
    var e = new ArrayBuffer(5);
    var n = new DataView(e);
    n.setUint8(0, 16);
    n.setUint8(1, 255);
    n.setUint8(2, 18);
    n.setUint8(3, t / 256);
    n.setUint8(4, t % 256);
    return e
}

function getShutTime() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 19);
    return t
}

function setPaperType(t, e) {
    var n = new ArrayBuffer(4);
    var i = new DataView(n);
    i.setUint8(0, 31);
    i.setUint8(1, 128);
    i.setUint8(2, t);
    i.setUint8(3, e);
    return n
}

function adjustPositionAuto(t) {
    var e = new ArrayBuffer(3);
    var n = new DataView(e);
    n.setUint8(0, 31);
    n.setUint8(1, 17);
    n.setUint8(2, t);
    return e
}

function printImage(t, e, h, w, U) {
    wx.canvasGetImageData({
        canvasId: e, x: 0, y: 0, width: h, height: w, success: function (t) {
            var e = new DataView(t.data.buffer);
            var n = parseInt((t.width + 7) / 8);
            var i = new ArrayBuffer(8 + n * w, 0);
            var r = new DataView(i);
            r.setUint8(0, 29);
            r.setUint8(1, 118);
            r.setUint8(2, 48);
            r.setUint8(3, 0);
            r.setUint8(4, n % 256);
            r.setUint8(5, n / 256);
            r.setUint8(6, w % 256);
            r.setUint8(7, w / 256);
            var a = 200;
            for (var s = 0; s < t.height; s++) {
                for (var o = 0; o < n; o++) {
                    var c = 0;
                    for (var u = 0; u < 8; u++) {
                        if (o * 8 + u < h) {
                            var f = e.getUint8((h * s + o * 8 + u) * 4);
                            var d = e.getUint8((h * s + o * 8 + u) * 4 + 1);
                            var v = e.getUint8((h * s + o * 8 + u) * 4 + 2);
                            var l = e.getUint8((h * s + o * 8 + u) * 4 + 3);
                            var g = (f + d + v) / 3;
                            if (g != -1 && g <= a && l != 0) {
                                c |= 128 >> u
                            }
                        }
                    }
                    r.setUint8(8 + n * s + o, c)
                }
            }
            U(i)
        }, fail: function (t) {
            if (debug) console.log(t)
        }
    })
}

function printImageWithFast(t, e, n, i, r) {
    wx.canvasGetImageData({
        canvasId: e, x: 0, y: 0, width: n, height: i, success: function (t) {
            var e = kakaImage(t);
            r(e)
        }, fail: function (t) {
            if (debug) console.log(t)
        }
    })
}

function kakaImage(t) {
    var e = t.height;
    var n = t.width;
    var i = new DataView(t.data.buffer);
    var r = parseInt((t.width + 7) / 8);
    var a = new ArrayBuffer(r * e);
    var s = new DataView(a);
    var o = 200;
    for (var c = 0; c < t.height; c++) {
        for (var u = 0; u < r; u++) {
            var f = 0;
            for (var d = 0; d < 8; d++) {
                if (u * 8 + d < n) {
                    var v = i.getUint8((n * c + u * 8 + d) * 4);
                    var l = i.getUint8((n * c + u * 8 + d) * 4 + 1);
                    var g = i.getUint8((n * c + u * 8 + d) * 4 + 2);
                    var h = i.getUint8((n * c + u * 8 + d) * 4 + 3);
                    var w = (v + l + g) / 3;
                    if (w != -1 && w <= o && h != 0) {
                        f |= 128 >> d
                    }
                }
            }
            s.setUint8(r * c + u, f)
        }
    }
    var U = dada(a, {level: -1});
    var B = new ArrayBuffer(10 + U.length, 0);
    var I = new DataView(B);
    I.setUint8(0, 31);
    I.setUint8(1, 16);
    I.setUint8(2, r / 256);
    I.setUint8(3, r % 256);
    I.setUint8(4, e / 256);
    I.setUint8(5, e % 256);
    I.setUint8(6, U.length >> 24 & 255);
    I.setUint8(7, U.length >> 16 & 255);
    I.setUint8(8, U.length >> 8 & 255);
    I.setUint8(9, U.length & 255);
    for (var c = 0; c < U.length; c++) {
        I.setUint8(10 + c, U[c])
    }
    return B
}

function bmpdataFuction() {
    var t = new ArrayBuffer(4 * 32);
    var e = new DataView(t);
    for (var n = 0; n < 4 * 32; n++) {
        e.setUint8(n, 255)
    }
    var i = dada(t, {level: -1});
    var r = new ArrayBuffer(10 + i.length);
    var a = new DataView(r);
    a.setUint8(0, 31);
    a.setUint8(1, 16);
    a.setUint8(2, 0);
    a.setUint8(3, 4);
    a.setUint8(4, 0);
    a.setUint8(5, 32);
    a.setUint8(6, i.length >> 24 & 255);
    a.setUint8(7, i.length >> 16 & 255);
    a.setUint8(8, i.length >> 8 & 255);
    a.setUint8(9, i.length & 255);
    for (var n = 0; n < i.length; n++) {
        a.setUint8(10 + n, i[n])
    }
    if (debug) console.log(r);
    return r
}

function ZStream() {
    this.input = null;
    this.next_in = 0;
    this.avail_in = 0;
    this.total_in = 0;
    this.output = null;
    this.next_out = 0;
    this.avail_out = 0;
    this.total_out = 0;
    this.state = null;
    this.data_type = 2;
    this.adler = 0
}

function printP80(t, e) {
    if (e) feed();
    if (!t) {
        if (e) {
            let t = "FORM\r\n" + "PRINT\r\n";
            const n = gbToBase64(t);
            const i = wx.base64ToArrayBuffer(n);
            return i
        } else {
            let t = "PRINT\r\n";
            const n = gbToBase64(t);
            const i = wx.base64ToArrayBuffer(n);
            return i
        }
    } else {
        if (e) {
            let t = "FORM\r\n" + "POPRINT\r\n";
            const n = gbToBase64(t);
            const i = wx.base64ToArrayBuffer(n);
            return i
        } else {
            let t = "POPRINT\r\n";
            const n = gbToBase64(t);
            const i = wx.base64ToArrayBuffer(n);
            return i
        }
    }
}

function pageSetupP80(t, e) {
    let n = "! 0 200 200 " + e + " " + "1" + "PAGE-WIDTH " + t;
    const i = gbToBase64(n);
    const r = wx.base64ToArrayBuffer(i);
    return r
}

function printImageWithP80(t, e, n, i, r, a, s) {
    wx.canvasGetImageData({
        canvasId: e, x: 0, y: 0, width: r, height: a, success: function (t) {
            var e = kakaImageP80(n, i, t);
            s(e)
        }, fail: function (t) {
            if (debug) console.log(t)
        }
    })
}

function kakaImageP80(t, e, n) {
    var i = n.height;
    var r = n.width;
    var a = new DataView(n.data.buffer);
    var s = parseInt((n.width + 7) / 8);
    var o = new ArrayBuffer(s * i);
    var c = new DataView(o);
    var u = 200;
    for (var f = 0; f < n.height; f++) {
        for (var d = 0; d < s; d++) {
            var v = 0;
            for (var l = 0; l < 8; l++) {
                if (d * 8 + l < r) {
                    var g = a.getUint8((r * f + d * 8 + l) * 4);
                    var h = a.getUint8((r * f + d * 8 + l) * 4 + 1);
                    var w = a.getUint8((r * f + d * 8 + l) * 4 + 2);
                    var U = a.getUint8((r * f + d * 8 + l) * 4 + 3);
                    var B = (g + h + w) / 3;
                    if (B != -1 && B <= u && U != 0) {
                        v |= 128 >> l
                    }
                }
            }
            c.setUint8(s * f + d, v)
        }
    }
    let I = "ZG " + s + " " + i + " " + t + " " + e + " ";
    const p = gbToBase64(I);
    const y = wx.base64ToArrayBuffer(p);
    var b = dada(o, {level: -1});
    var D = new ArrayBuffer(y.length + 4 + b.length, 0);
    var P = new DataView(D);
    for (var f = 0; f < y.length; f++) {
        P.setUint8(f, y[f])
    }
    P.setUint8(y.length + 0, b.length >> 24 & 255);
    P.setUint8(y.length + 1, b.length >> 16 & 255);
    P.setUint8(y.length + 2, b.length >> 8 & 255);
    P.setUint8(y.length + 3, b.length & 255);
    for (var f = 0; f < b.length; f++) {
        P.setUint8(y.length + 4 + f, b[f])
    }
    return D
}

module.exports = {
    connect: connect,
    disconnect: disconnect,
    onBLECharacteristicValueChange: onBLECharacteristicValueChange,
    onBLEConnectionStateChanged: onBLEConnectionStateChanged,
    onBluetoothAdapterStateChange: onBluetoothAdapterStateChange,
    sendData: sendData,
    adjustPosition: adjustPosition,
    printerPosition: printerPosition,
    printLinedots: printLinedots,
    getLabelHeight: getLabelHeight,
    setDensity: setDensity,
    learnLabelGap: learnLabelGap,
    setBTType: setBTType,
    startPrintjob: startPrintjob,
    stopPrintjob: stopPrintjob,
    gePrinterInfor: gePrinterInfor,
    getPrinterStatus: getPrinterStatus,
    getPrinterBatteryVol: getPrinterBatteryVol,
    getPrinterBtname: getPrinterBtname,
    getPrinterMac: getPrinterMac,
    getBtVersion: getBtVersion,
    getPrinterVersion: getPrinterVersion,
    getPrinterSN: getPrinterSN,
    getPrinterModel: getPrinterModel,
    printImage: printImage,
    printImageWithFast: printImageWithFast,
    setShutTime: setShutTime,
    getShutTime: getShutTime,
    setPaperType: setPaperType,
    adjustPositionAuto: adjustPositionAuto,
    printP80: printP80,
    pageSetupP80: pageSetupP80,
    printImageWithP80: printImageWithP80
};