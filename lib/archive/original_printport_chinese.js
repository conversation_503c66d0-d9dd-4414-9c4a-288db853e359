var myinterface = require('./original_interface_chinese.js');

//===========================
/*连接打印机
  params:
    deviceId:String ,设备的id
    success:function,成功的回调,回调参数:{ "msg": "成功", "deviceId": connectedDeviceId }
    fail:function,失败的回调，回调参数:{"msg":"失败"}
*/
function connect(device, success, fail){
  myinterface.connect(device, success, fail)
}
function onBLECharacteristicValueChange(callback){
  myinterface.onBLECharacteristicValueChange(callback)
}
function onBLEConnectionStateChanged(callback){
  myinterface.onBLEConnectionStateChanged(callback)
}
function onBluetoothAdapterStateChange(callback){
 myinterface.onBluetoothAdapterStateChange(callback)
}
/*断开连接
*/
function disconnect(){
  myinterface.disconnect()
}

function sendData (data,complete) {
  myinterface.sendData(data,complete)
}
//===========================

/**
 * 调整打印纸位置
 *
 * @param mode   调整方式
 *                 0x00: 进纸 （单位：像素点）
 *                 0x01: 进纸 （单位：mm）
 *                 0x10: 退纸 （单位：像素点）
 *                 0x11: 退纸 （单位：mm）
 * @param distance 走纸距离
 */
function adjustPosition(mode, distance){
    return myinterface.adjustPosition(mode, distance);
}
/**
 * 打印定位
 */
function printerPosition() {
  return myinterface.printerPosition();
}

/**
 * 走纸命令
 * @param lines 走纸行数
 */
function printLinedots(linedots) {
  return myinterface.printLinedots(linedots);
}
/**
 * 查询标签高度，必须要先走纸定位几张标签，建议先定位3张标签，等收到三个OK后再去调用此接口去获取标签高度，否则获取的不准* 确
 */

function getLabelHeight() {
  return myinterface.getLabelHeight();
}

/**
 * 设置打印机浓度(0-2)
 * 0:低浓度
 * 1:中浓度
 * 2:高浓度
 */
function setDensity(level) {
  return myinterface.setDensity(level);
}

/**
 * 设置蓝牙传输类型
 * @return 向打印机发送的数据
 */
function setBTType() {
  return myinterface.setBTType();
}
/**
 * 打印开始标志
 */
function startPrintjob() {
  return myinterface.startPrintjob();
}
/**
 * 打印结束标志
 */
function stopPrintjob() {
  return myinterface.stopPrintjob();
}

/**
     * 设置纸张类型
     *
     * @param model 设置方式
     *              0x01: 设置纸张类型，设置成功打印机返回”OK\r\n”，失败时打印机返回”ER\r\n”。
     *              0x02: 设置纸张类型，无返回值
     * @param type  纸张类型
     *              0x10: 连续纸
     *              0x20: 缝隙纸
     *              0x30: 黑标纸
     */
    
    function setPaperType(model, type){
        return myinterface.setPaperType(model,type);
    }
      /**
     * 自动调整打印纸位置
     *
     * @param model 调整方式
     *              0x50: 进纸
     *              0x51: 退纸
     */
function adjustPositionAuto(model){
    return myinterface.adjustPositionAuto(model);
      }
/**
 * 查询打印机状态
 * 0:打印机正常
 * 其他（根据"位"判断打印机状态）
 * 第0位：1：正在打印
 * 第1位：1：纸舱盖开
 * 第2位：1：缺纸
 * 第3位：1：电池电压低
 * 第4位：1：打印头过热
 * 第5位：缺省（默认0）
 * 第6位：缺省（默认0）
 * 第7位：缺省（默认0）
 */
function getPrinterStatus() {
  return myinterface.getPrinterStatus();
}

/**
 * 查询打印机电池电压
 *电压(返回百分比)
 */
function getPrinterBatteryVol() {
  return myinterface.getPrinterBatteryVol();
}
/**
 * 查询打印机固件版本
 *
 */
function getPrinterVersion() {
  return myinterface.getPrinterVersion();
}
/**
 * 查询打印机SN
 */
function getPrinterSN() {
  return myinterface.getPrinterSN();
}
function setShutTime(time) {
  return myinterface.setShutTime(time);
}
function getShutTime() {
  return myinterface.getShutTime();
}
/**
 * 低速传输图片
 * @param id canvasId
 * @param widthbmp 图片宽度
 * @param heightbmp 图片高度
 * @param callback 打印回调
 * @return 向打印机发送的数据
 */
function printImage(id, widthbmp, heightbmp, callback) {
  const ctx = wx.createCanvasContext(id)
  return myinterface.printImageWithFast(ctx, id, widthbmp, heightbmp, callback);
}

function drawImage(ctx, filepath, startx, starty) {
  // const ctx = wx.createCanvasContext('testimage')
  wx.getImageInfo({
    src: filepath,
    success: function (res) {
      var widthbmp = res.width;
      var heightbmp = res.height;
      ctx.drawImage(filepath, startx, starty, widthbmp, heightbmp);
    }
  })
}
//合并byte数组
function merge( data1, data2) {
  var dataView1=new DataView(data1);
  var dataView2 = new DataView(data2);
  var buffer = new ArrayBuffer(dataView1.byteLength + dataView2.byteLength);
  var dataView = new DataView(buffer)
  for (var i = 0; i < dataView.byteLength; i++) {
    if (i < dataView1.byteLength) {
      dataView.setUint8(i, dataView1.getInt8(i));
    } else {
      dataView.setUint8(i, dataView2.getInt8(i - dataView1.byteLength));
    }
  }
  return buffer;
}
/*开始打印
  params:
    revervse:bool, 是否反向打印，true反向，false正向
    skip:bool,是否定位到标签，true定位，false不定位
*/
function printP80(revervse, skip) {
    return myinterface.printP80(revervse, skip);
  }
  /*设置打印纸张大小（打印区域）的大小
    params:
      pageWidth:number 打印区域宽度
      pageHeight:number 是否定位到标签，true定位，false不定位
  */
  function pageSetupP80(pageWidth, pageHeight){
    return myinterface.pageSetupP80(pageWidth, pageHeight);
  }
  
/**
 * 高速传输图片
 * @param id canvasId
 * @param widthbmp 图片宽度
 * @param heightbmp 图片高度
 * @param callback 打印回调
 * @return 向打印机发送的数据
 */
function printImageWithP80(id, start_x,start_y,widthbmp, heightbmp, callback) {
    const ctx = wx.createCanvasContext(id)
  return myinterface.printImageWithP80(ctx, id, start_x,start_y,widthbmp, heightbmp, callback);
}

module.exports = {
  connect:connect,
  disconnect:disconnect,
  onBLECharacteristicValueChange:onBLECharacteristicValueChange,
  onBLEConnectionStateChanged:onBLEConnectionStateChanged,
  onBluetoothAdapterStateChange:onBluetoothAdapterStateChange,
  sendData:sendData,
  adjustPosition:adjustPosition,
  printerPosition:printerPosition,
  printLinedots:printLinedots,
  getLabelHeight:getLabelHeight,
  setDensity: setDensity,
  setBTType: setBTType,
  startPrintjob: startPrintjob,
  stopPrintjob: stopPrintjob,
  getPrinterStatus:getPrinterStatus,
  getPrinterBatteryVol:getPrinterBatteryVol,
  getPrinterVersion:getPrinterVersion,
  getPrinterSN:getPrinterSN,
  printImage: printImage,
  drawImage: drawImage,
  setShutTime: setShutTime,
  getShutTime: getShutTime,
  merge: merge,
  printP80:printP80,
  pageSetupP80:pageSetupP80,
  printImageWithP80:printImageWithP80
};