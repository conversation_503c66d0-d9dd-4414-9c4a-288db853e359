(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
"use strict";
var TYPED_OK =
    "undefined" != typeof Uint8Array &&
    "undefined" != typeof Uint16Array &&
    "undefined" != typeof Int32Array;

function _has(r, t) {
    return Object.prototype.hasOwnProperty.call(r, t);
}

(exports.assign = function (r) {
    for (var t = Array.prototype.slice.call(arguments, 1); t.length;) {
        var e = t.shift();
        if (e) {
            if ("object" != typeof e) throw new TypeError(e + "must be non-object");
            for (var n in e) _has(e, n) && (r[n] = e[n]);
        }
    }
    return r;
}),
    (exports.shrinkBuf = function (r, t) {
        return r.length === t
            ? r
            : r.subarray
                ? r.subarray(0, t)
                : ((r.length = t), r);
    });
var fnTyped = {
        arraySet: function (r, t, e, n, a) {
            if (t.subarray && r.subarray) r.set(t.subarray(e, e + n), a);
            else for (var o = 0; o < n; o++) r[a + o] = t[e + o];
        },
        flattenChunks: function (r) {
            var t, e, n, a, o, s;
            for (t = n = 0, e = r.length; t < e; t++) n += r[t].length;
            for (s = new Uint8Array(n), t = a = 0, e = r.length; t < e; t++)
                (o = r[t]), s.set(o, a), (a += o.length);
            return s;
        },
    },
    fnUntyped = {
        arraySet: function (r, t, e, n, a) {
            for (var o = 0; o < n; o++) r[a + o] = t[e + o];
        },
        flattenChunks: function (r) {
            return [].concat.apply([], r);
        },
    };
(exports.setTyped = function (r) {
    r
        ? ((exports.Buf8 = Uint8Array),
            (exports.Buf16 = Uint16Array),
            (exports.Buf32 = Int32Array),
            exports.assign(exports, fnTyped))
        : ((exports.Buf8 = Array),
            (exports.Buf16 = Array),
            (exports.Buf32 = Array),
            exports.assign(exports, fnUntyped));
}),
    exports.setTyped(TYPED_OK);

},{}],2:[function(require,module,exports){
"use strict";
var configuration_table,
    utils = require("./common"),
    D_CODES = 30,
    HEAP_SIZE = 2 * (L_CODES = 286) + 1;

function err(t, e) {
    return e;
}

function rank(t) {
    return (t << 1) - (4 < t ? 9 : 0);
}

function zero(t) {
    for (var e = t.length; 0 <= --e;) t[e] = 0;
}

function flush_pending(t) {
    var e = t.state,
        a = e.pending;
    a > t.avail_out && (a = t.avail_out),
    0 !== a &&
    (utils.arraySet(t.output, e.pending_buf, e.pending_out, a, t.next_out),
        (t.next_out += a),
        (e.pending_out += a),
        (t.total_out += a),
        (t.avail_out -= a),
        (e.pending -= a),
    0 === e.pending && (e.pending_out = 0));
}

function flush_block_only(t, e) {
    _tr_flush_block(
        t,
        0 <= t.block_start ? t.block_start : -1,
        t.strstart - t.block_start,
        e
    ),
        (t.block_start = t.strstart),
        flush_pending(t.strm);
}

function put_byte(t, e) {
    t.pending_buf[t.pending++] = e;
}

function putShortMSB(t, e) {
    (t.pending_buf[t.pending++] = (e >>> 8) & 255),
        (t.pending_buf[t.pending++] = 255 & e);
}

function read_buf(t, e, a, _) {
    var s = t.avail_in;
    return (
        _ < s && (s = _),
            0 === s
                ? 0
                : ((t.avail_in -= s),
                    utils.arraySet(e, t.input, t.next_in, s, a),
                    1 === t.state.wrap
                        ? (t.adler = adler32(t.adler, e, s, a))
                        : 2 === t.state.wrap && (t.adler = crc32(t.adler, e, s, a)),
                    (t.next_in += s),
                    (t.total_in += s),
                    s)
    );
}

function longest_match(t, e) {
    var a,
        _,
        s = t.max_chain_length,
        n = t.strstart,
        r = t.prev_length,
        i = t.nice_match,
        l = t.strstart > t.w_size - 262 ? t.strstart - (t.w_size - 262) : 0,
        d = t.window,
        h = t.w_mask,
        o = t.prev,
        u = t.strstart + 258,
        c = d[n + r - 1],
        f = d[n + r];
    t.prev_length >= t.good_match && (s >>= 2),
    i > t.lookahead && (i = t.lookahead);
    do {
        if (
            d[(a = e) + r] === f &&
            d[a + r - 1] === c &&
            d[a] === d[n] &&
            d[++a] === d[n + 1]
        ) {
            (n += 2), a++;
            do {
            } while (
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                d[++n] === d[++a] &&
                n < u
                );
            if (((_ = 258 - (u - n)), (n = u - 258), r < _)) {
                if (((t.match_start = e), i <= (r = _))) break;
                (c = d[n + r - 1]), (f = d[n + r]);
            }
        }
    } while ((e = o[e & h]) > l && 0 != --s);
    return r <= t.lookahead ? r : t.lookahead;
}

function fill_window(t) {
    var e,
        a,
        _,
        s,
        n,
        r = t.w_size;
    do {
        if (
            ((s = t.window_size - t.lookahead - t.strstart),
            t.strstart >= r + (r - 262))
        ) {
            for (
                utils.arraySet(t.window, t.window, r, r, 0),
                    t.match_start -= r,
                    t.strstart -= r,
                    t.block_start -= r,
                    e = a = t.hash_size;
                (_ = t.head[--e]), (t.head[e] = r <= _ ? _ - r : 0), --a;
            ) ;
            for (
                e = a = r;
                (_ = t.prev[--e]), (t.prev[e] = r <= _ ? _ - r : 0), --a;
            ) ;
            s += r;
        }
        if (0 === t.strm.avail_in) break;
        if (
            ((a = read_buf(t.strm, t.window, t.strstart + t.lookahead, s)),
                (t.lookahead += a),
            3 <= t.lookahead + t.insert)
        )
            for (
                n = t.strstart - t.insert,
                    t.ins_h = t.window[n],
                    t.ins_h = ((t.ins_h << t.hash_shift) ^ t.window[n + 1]) & t.hash_mask;
                t.insert &&
                ((t.ins_h =
                    ((t.ins_h << t.hash_shift) ^ t.window[n + 3 - 1]) & t.hash_mask),
                    (t.prev[n & t.w_mask] = t.head[t.ins_h]),
                    (t.head[t.ins_h] = n),
                    n++,
                    t.insert--,
                    !(t.lookahead + t.insert < 3));
            ) ;
    } while (t.lookahead < 262 && 0 !== t.strm.avail_in);
}

function dudu_stored(t, e) {
    var a = 65535;
    for (a > t.pending_buf_size - 5 && (a = t.pending_buf_size - 5); ;) {
        if (t.lookahead <= 1) {
            if ((fill_window(t), 0 === t.lookahead && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        (t.strstart += t.lookahead), (t.lookahead = 0);
        var _ = t.block_start + a;
        if (
            (0 === t.strstart || t.strstart >= _) &&
            ((t.lookahead = t.strstart - _),
                (t.strstart = _),
                flush_block_only(t, !1),
            0 === t.strm.avail_out)
        )
            return 1;
        if (
            t.strstart - t.block_start >= t.w_size - 262 &&
            (flush_block_only(t, !1), 0 === t.strm.avail_out)
        )
            return 1;
    }
    return (
        (t.insert = 0),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : (t.strstart > t.block_start &&
                (flush_block_only(t, !1), t.strm.avail_out),
                    1)
    );
}

function dudu_fast(t, e) {
    for (var a, _; ;) {
        if (t.lookahead < 262) {
            if ((fill_window(t), t.lookahead < 262 && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        if (
            ((a = 0),
            3 <= t.lookahead &&
            ((t.ins_h =
                ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                t.hash_mask),
                (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                (t.head[t.ins_h] = t.strstart)),
            0 !== a &&
            t.strstart - a <= t.w_size - 262 &&
            (t.match_length = longest_match(t, a)),
            3 <= t.match_length)
        )
            if (
                ((_ = _tr_tally(t, t.strstart - t.match_start, t.match_length - 3)),
                    (t.lookahead -= t.match_length),
                t.match_length <= t.max_lazy_match && 3 <= t.lookahead)
            ) {
                for (
                    t.match_length--;
                    t.strstart++,
                        (t.ins_h =
                            ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                            t.hash_mask),
                        (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                        (t.head[t.ins_h] = t.strstart),
                    0 != --t.match_length;
                ) ;
                t.strstart++;
            } else
                (t.strstart += t.match_length),
                    (t.match_length = 0),
                    (t.ins_h = t.window[t.strstart]),
                    (t.ins_h =
                        ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 1]) &
                        t.hash_mask);
        else
            (_ = _tr_tally(t, 0, t.window[t.strstart])), t.lookahead--, t.strstart++;
        if (_ && (flush_block_only(t, !1), 0 === t.strm.avail_out)) return 1;
    }
    return (
        (t.insert = t.strstart < 2 ? t.strstart : 2),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function dudu_slow(t, e) {
    for (var a, _, s; ;) {
        if (t.lookahead < 262) {
            if ((fill_window(t), t.lookahead < 262 && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        if (
            ((a = 0),
            3 <= t.lookahead &&
            ((t.ins_h =
                ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                t.hash_mask),
                (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                (t.head[t.ins_h] = t.strstart)),
                (t.prev_length = t.match_length),
                (t.prev_match = t.match_start),
                (t.match_length = 2),
            0 !== a &&
            t.prev_length < t.max_lazy_match &&
            t.strstart - a <= t.w_size - 262 &&
            ((t.match_length = longest_match(t, a)),
            t.match_length <= 5 &&
            (1 === t.strategy ||
                (3 === t.match_length && 4096 < t.strstart - t.match_start)) &&
            (t.match_length = 2)),
            3 <= t.prev_length && t.match_length <= t.prev_length)
        ) {
            for (
                s = t.strstart + t.lookahead - 3,
                    _ = _tr_tally(t, t.strstart - 1 - t.prev_match, t.prev_length - 3),
                    t.lookahead -= t.prev_length - 1,
                    t.prev_length -= 2;
                ++t.strstart <= s &&
                ((t.ins_h =
                    ((t.ins_h << t.hash_shift) ^ t.window[t.strstart + 3 - 1]) &
                    t.hash_mask),
                    (a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h]),
                    (t.head[t.ins_h] = t.strstart)),
                0 != --t.prev_length;
            ) ;
            if (
                ((t.match_available = 0),
                    (t.match_length = 2),
                    t.strstart++,
                _ && (flush_block_only(t, !1), 0 === t.strm.avail_out))
            )
                return 1;
        } else if (t.match_available) {
            if (
                ((_ = _tr_tally(t, 0, t.window[t.strstart - 1])) &&
                flush_block_only(t, !1),
                    t.strstart++,
                    t.lookahead--,
                0 === t.strm.avail_out)
            )
                return 1;
        } else (t.match_available = 1), t.strstart++, t.lookahead--;
    }
    return (
        t.match_available &&
        ((_ = _tr_tally(t, 0, t.window[t.strstart - 1])),
            (t.match_available = 0)),
            (t.insert = t.strstart < 2 ? t.strstart : 2),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function dudu_rle(t, e) {
    for (var a, _, s, n, r = t.window; ;) {
        if (t.lookahead <= 258) {
            if ((fill_window(t), t.lookahead <= 258 && 0 === e)) return 1;
            if (0 === t.lookahead) break;
        }
        if (
            ((t.match_length = 0),
            3 <= t.lookahead &&
            0 < t.strstart &&
            (_ = r[(s = t.strstart - 1)]) === r[++s] &&
            _ === r[++s] &&
            _ === r[++s])
        ) {
            n = t.strstart + 258;
            do {
            } while (
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                _ === r[++s] &&
                s < n
                );
            (t.match_length = 258 - (n - s)),
            t.match_length > t.lookahead && (t.match_length = t.lookahead);
        }
        if (
            (3 <= t.match_length
                ? ((a = _tr_tally(t, 1, t.match_length - 3)),
                    (t.lookahead -= t.match_length),
                    (t.strstart += t.match_length),
                    (t.match_length = 0))
                : ((a = _tr_tally(t, 0, t.window[t.strstart])),
                    t.lookahead--,
                    t.strstart++),
            a && (flush_block_only(t, !1), 0 === t.strm.avail_out))
        )
            return 1;
    }
    return (
        (t.insert = 0),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function dudu_huff(t, e) {
    for (var a; ;) {
        if (0 === t.lookahead && (fill_window(t), 0 === t.lookahead)) {
            if (0 === e) return 1;
            break;
        }
        if (
            ((t.match_length = 0),
                (a = _tr_tally(t, 0, t.window[t.strstart])),
                t.lookahead--,
                t.strstart++,
            a && (flush_block_only(t, !1), 0 === t.strm.avail_out))
        )
            return 1;
    }
    return (
        (t.insert = 0),
            4 === e
                ? (flush_block_only(t, !0), 0 === t.strm.avail_out ? 3 : 4)
                : t.last_lit && (flush_block_only(t, !1), 0 === t.strm.avail_out)
                    ? 1
                    : 2
    );
}

function Config(t, e, a, _, s) {
    (this.good_length = t),
        (this.max_lazy = e),
        (this.nice_length = a),
        (this.max_chain = _),
        (this.func = s);
}

function lm_init(t) {
    (t.window_size = 2 * t.w_size),
        zero(t.head),
        (t.max_lazy_match = configuration_table[t.level].max_lazy),
        (t.good_match = configuration_table[t.level].good_length),
        (t.nice_match = configuration_table[t.level].nice_length),
        (t.max_chain_length = configuration_table[t.level].max_chain),
        (t.strstart = 0),
        (t.block_start = 0),
        (t.lookahead = 0),
        (t.insert = 0),
        (t.match_length = t.prev_length = 2),
        (t.match_available = 0),
        (t.ins_h = 0);
}

function duduState() {
    (this.strm = null),
        (this.status = 0),
        (this.pending_buf = null),
        (this.pending_buf_size = 0),
        (this.pending_out = 0),
        (this.pending = 0),
        (this.wrap = 0),
        (this.gzhead = null),
        (this.gzindex = 0),
        (this.method = 8),
        (this.last_flush = -1),
        (this.w_size = 0),
        (this.w_bits = 0),
        (this.w_mask = 0),
        (this.window = null),
        (this.window_size = 0),
        (this.prev = null),
        (this.head = null),
        (this.ins_h = 0),
        (this.hash_size = 0),
        (this.hash_bits = 0),
        (this.hash_mask = 0),
        (this.hash_shift = 0),
        (this.block_start = 0),
        (this.match_length = 0),
        (this.prev_match = 0),
        (this.match_available = 0),
        (this.strstart = 0),
        (this.match_start = 0),
        (this.lookahead = 0),
        (this.prev_length = 0),
        (this.max_chain_length = 0),
        (this.max_lazy_match = 0),
        (this.level = 0),
        (this.strategy = 0),
        (this.good_match = 0),
        (this.nice_match = 0),
        (this.dyn_ltree = new utils.Buf16(2 * HEAP_SIZE)),
        (this.dyn_dtree = new utils.Buf16(2 * (2 * D_CODES + 1))),
        (this.bl_tree = new utils.Buf16(78)),
        zero(this.dyn_ltree),
        zero(this.dyn_dtree),
        zero(this.bl_tree),
        (this.l_desc = null),
        (this.d_desc = null),
        (this.bl_desc = null),
        (this.bl_count = new utils.Buf16(16)),
        (this.heap = new utils.Buf16(2 * L_CODES + 1)),
        zero(this.heap),
        (this.heap_len = 0),
        (this.heap_max = 0),
        (this.depth = new utils.Buf16(2 * L_CODES + 1)),
        zero(this.depth),
        (this.l_buf = 0),
        (this.lit_bufsize = 0),
        (this.last_lit = 0),
        (this.d_buf = 0),
        (this.opt_len = 0),
        (this.static_len = 0),
        (this.matches = 0),
        (this.insert = 0),
        (this.bi_buf = 0),
        (this.bi_valid = 0);
}

function duduResetKeep(t) {
    var e;
    return t && t.state
        ? ((t.total_in = t.total_out = 0),
            (t.data_type = 2),
            ((e = t.state).pending = 0),
            (e.pending_out = 0),
        e.wrap < 0 && (e.wrap = -e.wrap),
            (e.status = e.wrap ? 42 : 113),
            (t.adler = 2 === e.wrap ? 0 : 1),
            (e.last_flush = 0),
            _tr_init(e),
            0)
        : err(t, -2);
}

function duduReset(t) {
    var e = duduResetKeep(t);
    return 0 === e && lm_init(t.state), e;
}

function duduSetHeader(t, e) {
    return t && t.state
        ? 2 !== t.state.wrap
            ? -2
            : ((t.state.gzhead = e), 0)
        : -2;
}

function duduInit2(t, e, a, _, s, n) {
    if (!t) return -2;
    var r = 1;
    if (
        (-1 === e && (e = 6),
            _ < 0 ? ((r = 0), (_ = -_)) : 15 < _ && ((r = 2), (_ -= 16)),
        s < 1 ||
        9 < s ||
        8 !== a ||
        _ < 8 ||
        15 < _ ||
        e < 0 ||
        9 < e ||
        n < 0 ||
        4 < n)
    )
        return err(t, -2);
    8 === _ && (_ = 9);
    var i = new duduState();
    return (
        ((t.state = i).strm = t),
            (i.wrap = r),
            (i.gzhead = null),
            (i.w_bits = _),
            (i.w_size = 1 << i.w_bits),
            (i.w_mask = i.w_size - 1),
            (i.hash_bits = s + 7),
            (i.hash_size = 1 << i.hash_bits),
            (i.hash_mask = i.hash_size - 1),
            (i.hash_shift = ~~((i.hash_bits + 3 - 1) / 3)),
            (i.window = new utils.Buf8(2 * i.w_size)),
            (i.head = new utils.Buf16(i.hash_size)),
            (i.prev = new utils.Buf16(i.w_size)),
            (i.lit_bufsize = 1 << (s + 6)),
            (i.pending_buf_size = 4 * i.lit_bufsize),
            (i.pending_buf = new utils.Buf8(i.pending_buf_size)),
            (i.d_buf = 1 * i.lit_bufsize),
            (i.l_buf = 3 * i.lit_bufsize),
            (i.level = e),
            (i.strategy = n),
            (i.method = a),
            duduReset(t)
    );
}

function duduInit(t, e) {
    return duduInit2(t, e, 8, 10, 8, 0);
}

function dudu(t, e) {
    var a, _, s, n;
    if (!t || !t.state || 5 < e || e < 0) return t ? err(t, -2) : -2;
    if (
        ((_ = t.state),
        !t.output ||
        (!t.input && 0 !== t.avail_in) ||
        (666 === _.status && 4 !== e))
    )
        return err(t, 0 === t.avail_out ? -5 : -2);
    if (((_.strm = t), (a = _.last_flush), (_.last_flush = e), 42 === _.status))
        if (2 === _.wrap)
            (t.adler = 0),
                put_byte(_, 31),
                put_byte(_, 139),
                put_byte(_, 8),
                _.gzhead
                    ? (put_byte(
                        _,
                        (_.gzhead.text ? 1 : 0) +
                        (_.gzhead.hcrc ? 2 : 0) +
                        (_.gzhead.extra ? 4 : 0) +
                        (_.gzhead.name ? 8 : 0) +
                        (_.gzhead.comment ? 16 : 0)
                    ),
                        put_byte(_, 255 & _.gzhead.time),
                        put_byte(_, (_.gzhead.time >> 8) & 255),
                        put_byte(_, (_.gzhead.time >> 16) & 255),
                        put_byte(_, (_.gzhead.time >> 24) & 255),
                        put_byte(
                            _,
                            9 === _.level ? 2 : 2 <= _.strategy || _.level < 2 ? 4 : 0
                        ),
                        put_byte(_, 255 & _.gzhead.os),
                    _.gzhead.extra &&
                    _.gzhead.extra.length &&
                    (put_byte(_, 255 & _.gzhead.extra.length),
                        put_byte(_, (_.gzhead.extra.length >> 8) & 255)),
                    _.gzhead.hcrc &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending, 0)),
                        (_.gzindex = 0),
                        (_.status = 69))
                    : (put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(_, 0),
                        put_byte(
                            _,
                            9 === _.level ? 2 : 2 <= _.strategy || _.level < 2 ? 4 : 0
                        ),
                        put_byte(_, 3),
                        (_.status = 113));
        else {
            var r = (8 + ((_.w_bits - 8) << 4)) << 8;
            (r |=
                (2 <= _.strategy || _.level < 2
                    ? 0
                    : _.level < 6
                        ? 1
                        : 6 === _.level
                            ? 2
                            : 3) << 6),
            0 !== _.strstart && (r |= 32),
                (r += 31 - (r % 31)),
                (_.status = 113),
                putShortMSB(_, r),
            0 !== _.strstart &&
            (putShortMSB(_, t.adler >>> 16), putShortMSB(_, 65535 & t.adler)),
                (t.adler = 1);
        }
    if (69 === _.status)
        if (_.gzhead.extra) {
            for (
                s = _.pending;
                _.gzindex < (65535 & _.gzhead.extra.length) &&
                (_.pending !== _.pending_buf_size ||
                    (_.gzhead.hcrc &&
                    _.pending > s &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
                        flush_pending(t),
                        (s = _.pending),
                    _.pending !== _.pending_buf_size));
            )
                put_byte(_, 255 & _.gzhead.extra[_.gzindex]), _.gzindex++;
            _.gzhead.hcrc &&
            _.pending > s &&
            (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
            _.gzindex === _.gzhead.extra.length &&
            ((_.gzindex = 0), (_.status = 73));
        } else _.status = 73;
    if (73 === _.status)
        if (_.gzhead.name) {
            s = _.pending;
            do {
                if (
                    _.pending === _.pending_buf_size &&
                    (_.gzhead.hcrc &&
                    _.pending > s &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
                        flush_pending(t),
                        (s = _.pending),
                    _.pending === _.pending_buf_size)
                ) {
                    n = 1;
                    break;
                }
                put_byte(
                    _,
                    (n =
                        _.gzindex < _.gzhead.name.length
                            ? 255 & _.gzhead.name.charCodeAt(_.gzindex++)
                            : 0)
                );
            } while (0 !== n);
            _.gzhead.hcrc &&
            _.pending > s &&
            (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
            0 === n && ((_.gzindex = 0), (_.status = 91));
        } else _.status = 91;
    if (91 === _.status)
        if (_.gzhead.comment) {
            s = _.pending;
            do {
                if (
                    _.pending === _.pending_buf_size &&
                    (_.gzhead.hcrc &&
                    _.pending > s &&
                    (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
                        flush_pending(t),
                        (s = _.pending),
                    _.pending === _.pending_buf_size)
                ) {
                    n = 1;
                    break;
                }
                put_byte(
                    _,
                    (n =
                        _.gzindex < _.gzhead.comment.length
                            ? 255 & _.gzhead.comment.charCodeAt(_.gzindex++)
                            : 0)
                );
            } while (0 !== n);
            _.gzhead.hcrc &&
            _.pending > s &&
            (t.adler = crc32(t.adler, _.pending_buf, _.pending - s, s)),
            0 === n && (_.status = 103);
        } else _.status = 103;
    if (
        (103 === _.status &&
        (_.gzhead.hcrc
            ? (_.pending + 2 > _.pending_buf_size && flush_pending(t),
            _.pending + 2 <= _.pending_buf_size &&
            (put_byte(_, 255 & t.adler),
                put_byte(_, (t.adler >> 8) & 255),
                (t.adler = 0),
                (_.status = 113)))
            : (_.status = 113)),
        0 !== _.pending)
    ) {
        if ((flush_pending(t), 0 === t.avail_out)) return (_.last_flush = -1), 0;
    } else if (0 === t.avail_in && rank(e) <= rank(a) && 4 !== e)
        return err(t, -5);
    if (666 === _.status && 0 !== t.avail_in) return err(t, -5);
    if (0 !== t.avail_in || 0 !== _.lookahead || (0 !== e && 666 !== _.status)) {
        var i =
            2 === _.strategy
                ? dudu_huff(_, e)
                : 3 === _.strategy
                    ? dudu_rle(_, e)
                    : configuration_table[_.level].func(_, e);
        if (((3 !== i && 4 !== i) || (_.status = 666), 1 === i || 3 === i))
            return 0 === t.avail_out && (_.last_flush = -1), 0;
        if (
            2 === i &&
            (1 === e
                ? _tr_align(_)
                : 5 !== e &&
                (_tr_stored_block(_, 0, 0, !1),
                3 === e &&
                (zero(_.head),
                0 === _.lookahead &&
                ((_.strstart = 0), (_.block_start = 0), (_.insert = 0)))),
                flush_pending(t),
            0 === t.avail_out)
        )
            return (_.last_flush = -1), 0;
    }
    return 4 !== e
        ? 0
        : _.wrap <= 0
            ? 1
            : (2 === _.wrap
                ? (put_byte(_, 255 & t.adler),
                    put_byte(_, (t.adler >> 8) & 255),
                    put_byte(_, (t.adler >> 16) & 255),
                    put_byte(_, (t.adler >> 24) & 255),
                    put_byte(_, 255 & t.total_in),
                    put_byte(_, (t.total_in >> 8) & 255),
                    put_byte(_, (t.total_in >> 16) & 255),
                    put_byte(_, (t.total_in >> 24) & 255))
                : (putShortMSB(_, t.adler >>> 16), putShortMSB(_, 65535 & t.adler)),
                flush_pending(t),
            0 < _.wrap && (_.wrap = -_.wrap),
                0 !== _.pending ? 0 : 1);
}

function duduEnd(t) {
    var e;
    return t && t.state
        ? 42 !== (e = t.state.status) &&
        69 !== e &&
        73 !== e &&
        91 !== e &&
        103 !== e &&
        113 !== e &&
        666 !== e
            ? err(t, -2)
            : ((t.state = null), 113 === e ? err(t, -3) : 0)
        : -2;
}

function duduSetDictionary(t, e) {
    var a,
        _,
        s,
        n,
        r,
        i,
        l,
        d,
        h = e.length;
    if (!t || !t.state) return -2;
    if (
        2 === (n = (a = t.state).wrap) ||
        (1 === n && 42 !== a.status) ||
        a.lookahead
    )
        return -2;
    for (
        1 === n && (t.adler = adler32(t.adler, e, h, 0)),
            a.wrap = 0,
        h >= a.w_size &&
        (0 === n &&
        (zero(a.head), (a.strstart = 0), (a.block_start = 0), (a.insert = 0)),
            (d = new utils.Buf8(a.w_size)),
            utils.arraySet(d, e, h - a.w_size, a.w_size, 0),
            (e = d),
            (h = a.w_size)),
            r = t.avail_in,
            i = t.next_in,
            l = t.input,
            t.avail_in = h,
            t.next_in = 0,
            t.input = e,
            fill_window(a);
        3 <= a.lookahead;
    ) {
        for (
            _ = a.strstart, s = a.lookahead - 2;
            (a.ins_h =
                ((a.ins_h << a.hash_shift) ^ a.window[_ + 3 - 1]) & a.hash_mask),
                (a.prev[_ & a.w_mask] = a.head[a.ins_h]),
                (a.head[a.ins_h] = _),
                _++,
                --s;
        ) ;
        (a.strstart = _), (a.lookahead = 2), fill_window(a);
    }
    return (
        (a.strstart += a.lookahead),
            (a.block_start = a.strstart),
            (a.insert = a.lookahead),
            (a.lookahead = 0),
            (a.match_length = a.prev_length = 2),
            (a.match_available = 0),
            (t.next_in = i),
            (t.input = l),
            (t.avail_in = r),
            (a.wrap = n),
            0
    );
}

function adler32(t, e, a, _) {
    for (
        var s = (65535 & t) | 0, n = ((t >>> 16) & 65535) | 0, r = 0;
        0 !== a;
    ) {
        for (
            a -= r = 2e3 < a ? 2e3 : a;
            (n = (n + (s = (s + e[_++]) | 0)) | 0), --r;
        ) ;
        (s %= 65521), (n %= 65521);
    }
    return s | (n << 16) | 0;
}

function makeTable() {
    for (var t, e = [], a = 0; a < 256; a++) {
        t = a;
        for (var _ = 0; _ < 8; _++) t = 1 & t ? 3988292384 ^ (t >>> 1) : t >>> 1;
        e[a] = t;
    }
    return e;
}

configuration_table = [
    new Config(0, 0, 0, 0, dudu_stored),
    new Config(4, 4, 8, 4, dudu_fast),
    new Config(4, 5, 16, 8, dudu_fast),
    new Config(4, 6, 32, 32, dudu_fast),
    new Config(4, 4, 16, 16, dudu_slow),
    new Config(8, 16, 32, 32, dudu_slow),
    new Config(8, 16, 128, 128, dudu_slow),
    new Config(8, 32, 128, 256, dudu_slow),
    new Config(32, 128, 258, 1024, dudu_slow),
    new Config(32, 258, 258, 4096, dudu_slow),
];
var crcTable = makeTable();

function crc32(t, e, a, _) {
    var s = crcTable,
        n = _ + a;
    t ^= -1;
    for (var r = _; r < n; r++) t = (t >>> 8) ^ s[255 & (t ^ e[r])];
    return -1 ^ t;
}

var Z_BINARY = 0,
    Z_TEXT = 1;

function zero1(t) {
    for (var e = t.length; 0 <= --e;) t[e] = 0;
}

var L_CODES,
    STORED_BLOCK = 0,
    STATIC_TREES = 1,
    DYN_TREES = 2,
    Buf_size = ((D_CODES = 30), (HEAP_SIZE = 2 * (L_CODES = 286) + 1), 16),
    MAX_BL_BITS = 7,
    END_BLOCK = 256,
    REP_3_6 = 16,
    REPZ_3_10 = 17,
    REPZ_11_138 = 18,
    extra_lbits = [
        0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5,
        5, 5, 5, 0,
    ],
    extra_dbits = [
        0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10,
        11, 11, 12, 12, 13, 13,
    ],
    extra_blbits = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7],
    bl_order = [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15],
    DIST_CODE_LEN = 512,
    static_ltree = new Array(2 * (L_CODES + 2));
zero1(static_ltree);
var static_dtree = new Array(2 * D_CODES);
zero1(static_dtree);
var _dist_code = new Array(DIST_CODE_LEN);
zero1(_dist_code);
var _length_code = new Array(256);
zero1(_length_code);
var base_length = new Array(29);
zero1(base_length);
var static_l_desc,
    static_d_desc,
    static_bl_desc,
    base_dist = new Array(D_CODES);

function StaticTreeDesc(t, e, a, _, s) {
    (this.static_tree = t),
        (this.extra_bits = e),
        (this.extra_base = a),
        (this.elems = _),
        (this.max_length = s),
        (this.has_stree = t && t.length);
}

function TreeDesc(t, e) {
    (this.dyn_tree = t), (this.max_code = 0), (this.stat_desc = e);
}

function d_code(t) {
    return t < 256 ? _dist_code[t] : _dist_code[256 + (t >>> 7)];
}

function put_short(t, e) {
    (t.pending_buf[t.pending++] = 255 & e),
        (t.pending_buf[t.pending++] = (e >>> 8) & 255);
}

function send_bits(t, e, a) {
    t.bi_valid > Buf_size - a
        ? ((t.bi_buf |= (e << t.bi_valid) & 65535),
            put_short(t, t.bi_buf),
            (t.bi_buf = e >> (Buf_size - t.bi_valid)),
            (t.bi_valid += a - Buf_size))
        : ((t.bi_buf |= (e << t.bi_valid) & 65535), (t.bi_valid += a));
}

function send_code(t, e, a) {
    send_bits(t, a[2 * e], a[2 * e + 1]);
}

function bi_reverse(t, e) {
    for (var a = 0; (a |= 1 & t), (t >>>= 1), (a <<= 1), 0 < --e;) ;
    return a >>> 1;
}

function bi_flush(t) {
    16 === t.bi_valid
        ? (put_short(t, t.bi_buf), (t.bi_buf = 0), (t.bi_valid = 0))
        : 8 <= t.bi_valid &&
        ((t.pending_buf[t.pending++] = 255 & t.bi_buf),
            (t.bi_buf >>= 8),
            (t.bi_valid -= 8));
}

function gen_bitlen(t, e) {
    var a,
        _,
        s,
        n,
        r,
        i,
        l = e.dyn_tree,
        d = e.max_code,
        h = e.stat_desc.static_tree,
        o = e.stat_desc.has_stree,
        u = e.stat_desc.extra_bits,
        c = e.stat_desc.extra_base,
        f = e.stat_desc.max_length,
        b = 0;
    for (n = 0; n <= 15; n++) t.bl_count[n] = 0;
    for (
        l[2 * t.heap[t.heap_max] + 1] = 0, a = t.heap_max + 1;
        a < HEAP_SIZE;
        a++
    )
        f < (n = l[2 * l[2 * (_ = t.heap[a]) + 1] + 1] + 1) && ((n = f), b++),
            (l[2 * _ + 1] = n),
        d < _ ||
        (t.bl_count[n]++,
            (r = 0),
        c <= _ && (r = u[_ - c]),
            (i = l[2 * _]),
            (t.opt_len += i * (n + r)),
        o && (t.static_len += i * (h[2 * _ + 1] + r)));
    if (0 !== b) {
        do {
            for (n = f - 1; 0 === t.bl_count[n];) n--;
            t.bl_count[n]--, (t.bl_count[n + 1] += 2), t.bl_count[f]--, (b -= 2);
        } while (0 < b);
        for (n = f; 0 !== n; n--)
            for (_ = t.bl_count[n]; 0 !== _;)
                d < (s = t.heap[--a]) ||
                (l[2 * s + 1] !== n &&
                ((t.opt_len += (n - l[2 * s + 1]) * l[2 * s]), (l[2 * s + 1] = n)),
                    _--);
    }
}

function gen_codes(t, e, a) {
    var _,
        s,
        n = new Array(16),
        r = 0;
    for (_ = 1; _ <= 15; _++) n[_] = r = (r + a[_ - 1]) << 1;
    for (s = 0; s <= e; s++) {
        var i = t[2 * s + 1];
        0 !== i && (t[2 * s] = bi_reverse(n[i]++, i));
    }
}

function tr_static_init() {
    var t,
        e,
        a,
        _,
        s,
        n = new Array(16);
    for (_ = a = 0; _ < 28; _++)
        for (base_length[_] = a, t = 0; t < 1 << extra_lbits[_]; t++)
            _length_code[a++] = _;
    for (_length_code[a - 1] = _, _ = s = 0; _ < 16; _++)
        for (base_dist[_] = s, t = 0; t < 1 << extra_dbits[_]; t++)
            _dist_code[s++] = _;
    for (s >>= 7; _ < D_CODES; _++)
        for (base_dist[_] = s << 7, t = 0; t < 1 << (extra_dbits[_] - 7); t++)
            _dist_code[256 + s++] = _;
    for (e = 0; e <= 15; e++) n[e] = 0;
    for (t = 0; t <= 143;) (static_ltree[2 * t + 1] = 8), t++, n[8]++;
    for (; t <= 255;) (static_ltree[2 * t + 1] = 9), t++, n[9]++;
    for (; t <= 279;) (static_ltree[2 * t + 1] = 7), t++, n[7]++;
    for (; t <= 287;) (static_ltree[2 * t + 1] = 8), t++, n[8]++;
    for (gen_codes(static_ltree, L_CODES + 1, n), t = 0; t < D_CODES; t++)
        (static_dtree[2 * t + 1] = 5), (static_dtree[2 * t] = bi_reverse(t, 5));
    (static_l_desc = new StaticTreeDesc(
        static_ltree,
        extra_lbits,
        257,
        L_CODES,
        15
    )),
        (static_d_desc = new StaticTreeDesc(
            static_dtree,
            extra_dbits,
            0,
            D_CODES,
            15
        )),
        (static_bl_desc = new StaticTreeDesc(
            new Array(0),
            extra_blbits,
            0,
            19,
            MAX_BL_BITS
        ));
}

function init_block(t) {
    var e;
    for (e = 0; e < L_CODES; e++) t.dyn_ltree[2 * e] = 0;
    for (e = 0; e < D_CODES; e++) t.dyn_dtree[2 * e] = 0;
    for (e = 0; e < 19; e++) t.bl_tree[2 * e] = 0;
    (t.dyn_ltree[2 * END_BLOCK] = 1),
        (t.opt_len = t.static_len = 0),
        (t.last_lit = t.matches = 0);
}

function bi_windup(t) {
    8 < t.bi_valid
        ? put_short(t, t.bi_buf)
        : 0 < t.bi_valid && (t.pending_buf[t.pending++] = t.bi_buf),
        (t.bi_buf = 0),
        (t.bi_valid = 0);
}

function copy_block(t, e, a, _) {
    bi_windup(t),
    _ && (put_short(t, a), put_short(t, ~a)),
        utils.arraySet(t.pending_buf, t.window, e, a, t.pending),
        (t.pending += a);
}

function smaller(t, e, a, _) {
    var s = 2 * e,
        n = 2 * a;
    return t[s] < t[n] || (t[s] === t[n] && _[e] <= _[a]);
}

function pqdownheap(t, e, a) {
    for (
        var _ = t.heap[a], s = a << 1;
        s <= t.heap_len &&
        (s < t.heap_len && smaller(e, t.heap[s + 1], t.heap[s], t.depth) && s++,
            !smaller(e, _, t.heap[s], t.depth));
    )
        (t.heap[a] = t.heap[s]), (a = s), (s <<= 1);
    t.heap[a] = _;
}

function compress_block(t, e, a) {
    var _,
        s,
        n,
        r,
        i = 0;
    if (0 !== t.last_lit)
        for (
            ;
            (_ =
                (t.pending_buf[t.d_buf + 2 * i] << 8) |
                t.pending_buf[t.d_buf + 2 * i + 1]),
                (s = t.pending_buf[t.l_buf + i]),
                i++,
                0 === _
                    ? send_code(t, s, e)
                    : (send_code(t, (n = _length_code[s]) + 256 + 1, e),
                    0 !== (r = extra_lbits[n]) &&
                    send_bits(t, (s -= base_length[n]), r),
                        send_code(t, (n = d_code(--_)), a),
                    0 !== (r = extra_dbits[n]) && send_bits(t, (_ -= base_dist[n]), r)),
            i < t.last_lit;
        ) ;
    send_code(t, END_BLOCK, e);
}

function build_tree(t, e) {
    var a,
        _,
        s,
        n = e.dyn_tree,
        r = e.stat_desc.static_tree,
        i = e.stat_desc.has_stree,
        l = e.stat_desc.elems,
        d = -1;
    for (t.heap_len = 0, t.heap_max = HEAP_SIZE, a = 0; a < l; a++)
        0 !== n[2 * a]
            ? ((t.heap[++t.heap_len] = d = a), (t.depth[a] = 0))
            : (n[2 * a + 1] = 0);
    for (; t.heap_len < 2;)
        (n[2 * (s = t.heap[++t.heap_len] = d < 2 ? ++d : 0)] = 1),
            (t.depth[s] = 0),
            t.opt_len--,
        i && (t.static_len -= r[2 * s + 1]);
    for (e.max_code = d, a = t.heap_len >> 1; 1 <= a; a--) pqdownheap(t, n, a);
    for (
        s = l;
        (a = t.heap[1]),
            (t.heap[1] = t.heap[t.heap_len--]),
            pqdownheap(t, n, 1),
            (_ = t.heap[1]),
            (t.heap[--t.heap_max] = a),
            (t.heap[--t.heap_max] = _),
            (n[2 * s] = n[2 * a] + n[2 * _]),
            (t.depth[s] = (t.depth[a] >= t.depth[_] ? t.depth[a] : t.depth[_]) + 1),
            (n[2 * a + 1] = n[2 * _ + 1] = s),
            (t.heap[1] = s++),
            pqdownheap(t, n, 1),
        2 <= t.heap_len;
    ) ;
    (t.heap[--t.heap_max] = t.heap[1]),
        gen_bitlen(t, e),
        gen_codes(n, d, t.bl_count);
}

function scan_tree(t, e, a) {
    var _,
        s,
        n = -1,
        r = e[1],
        i = 0,
        l = 7,
        d = 4;
    for (
        0 === r && ((l = 138), (d = 3)), e[2 * (a + 1) + 1] = 65535, _ = 0;
        _ <= a;
        _++
    )
        (s = r),
            (r = e[2 * (_ + 1) + 1]),
        (++i < l && s === r) ||
        (i < d
            ? (t.bl_tree[2 * s] += i)
            : 0 !== s
                ? (s !== n && t.bl_tree[2 * s]++, t.bl_tree[2 * REP_3_6]++)
                : i <= 10
                    ? t.bl_tree[2 * REPZ_3_10]++
                    : t.bl_tree[2 * REPZ_11_138]++,
            (n = s),
            (d =
                (i = 0) === r
                    ? ((l = 138), 3)
                    : s === r
                        ? ((l = 6), 3)
                        : ((l = 7), 4)));
}

function send_tree(t, e, a) {
    var _,
        s,
        n = -1,
        r = e[1],
        i = 0,
        l = 7,
        d = 4;
    for (0 === r && ((l = 138), (d = 3)), _ = 0; _ <= a; _++)
        if (((s = r), (r = e[2 * (_ + 1) + 1]), !(++i < l && s === r))) {
            if (i < d) for (; send_code(t, s, t.bl_tree), 0 != --i;) ;
            else
                0 !== s
                    ? (s !== n && (send_code(t, s, t.bl_tree), i--),
                        send_code(t, REP_3_6, t.bl_tree),
                        send_bits(t, i - 3, 2))
                    : i <= 10
                        ? (send_code(t, REPZ_3_10, t.bl_tree), send_bits(t, i - 3, 3))
                        : (send_code(t, REPZ_11_138, t.bl_tree), send_bits(t, i - 11, 7));
            (n = s),
                (d =
                    (i = 0) === r
                        ? ((l = 138), 3)
                        : s === r
                            ? ((l = 6), 3)
                            : ((l = 7), 4));
        }
}

function build_bl_tree(t) {
    var e;
    for (
        scan_tree(t, t.dyn_ltree, t.l_desc.max_code),
            scan_tree(t, t.dyn_dtree, t.d_desc.max_code),
            build_tree(t, t.bl_desc),
            e = 18;
        3 <= e && 0 === t.bl_tree[2 * bl_order[e] + 1];
        e--
    ) ;
    return (t.opt_len += 3 * (e + 1) + 5 + 5 + 4), e;
}

function send_all_trees(t, e, a, _) {
    var s;
    for (
        send_bits(t, e - 257, 5),
            send_bits(t, a - 1, 5),
            send_bits(t, _ - 4, 4),
            s = 0;
        s < _;
        s++
    )
        send_bits(t, t.bl_tree[2 * bl_order[s] + 1], 3);
    send_tree(t, t.dyn_ltree, e - 1), send_tree(t, t.dyn_dtree, a - 1);
}

function detect_data_type(t) {
    var e,
        a = 4093624447;
    for (e = 0; e <= 31; e++, a >>>= 1)
        if (1 & a && 0 !== t.dyn_ltree[2 * e]) return Z_BINARY;
    if (0 !== t.dyn_ltree[18] || 0 !== t.dyn_ltree[20] || 0 !== t.dyn_ltree[26])
        return Z_TEXT;
    for (e = 32; e < 256; e++) if (0 !== t.dyn_ltree[2 * e]) return Z_TEXT;
    return Z_BINARY;
}

zero1(base_dist);
var static_init_done = !1;

function _tr_init(t) {
    static_init_done || (tr_static_init(), (static_init_done = !0)),
        (t.l_desc = new TreeDesc(t.dyn_ltree, static_l_desc)),
        (t.d_desc = new TreeDesc(t.dyn_dtree, static_d_desc)),
        (t.bl_desc = new TreeDesc(t.bl_tree, static_bl_desc)),
        (t.bi_buf = 0),
        (t.bi_valid = 0),
        init_block(t);
}

function _tr_stored_block(t, e, a, _) {
    send_bits(t, (STORED_BLOCK << 1) + (_ ? 1 : 0), 3), copy_block(t, e, a, !0);
}

function _tr_align(t) {
    send_bits(t, STATIC_TREES << 1, 3),
        send_code(t, END_BLOCK, static_ltree),
        bi_flush(t);
}

function _tr_flush_block(t, e, a, _) {
    var s,
        n,
        r = 0;
    0 < t.level
        ? (2 === t.strm.data_type && (t.strm.data_type = detect_data_type(t)),
            build_tree(t, t.l_desc),
            build_tree(t, t.d_desc),
            (r = build_bl_tree(t)),
            (s = (t.opt_len + 3 + 7) >>> 3),
        (n = (t.static_len + 3 + 7) >>> 3) <= s && (s = n))
        : (s = n = a + 5),
        a + 4 <= s && -1 !== e
            ? _tr_stored_block(t, e, a, _)
            : 4 === t.strategy || n === s
                ? (send_bits(t, (STATIC_TREES << 1) + (_ ? 1 : 0), 3),
                    compress_block(t, static_ltree, static_dtree))
                : (send_bits(t, (DYN_TREES << 1) + (_ ? 1 : 0), 3),
                    send_all_trees(t, t.l_desc.max_code + 1, t.d_desc.max_code + 1, r + 1),
                    compress_block(t, t.dyn_ltree, t.dyn_dtree)),
        init_block(t),
    _ && bi_windup(t);
}

function _tr_tally(t, e, a) {
    return (
        (t.pending_buf[t.d_buf + 2 * t.last_lit] = (e >>> 8) & 255),
            (t.pending_buf[t.d_buf + 2 * t.last_lit + 1] = 255 & e),
            (t.pending_buf[t.l_buf + t.last_lit] = 255 & a),
            t.last_lit++,
            0 === e
                ? t.dyn_ltree[2 * a]++
                : (t.matches++,
                    e--,
                    t.dyn_ltree[2 * (_length_code[a] + 256 + 1)]++,
                    t.dyn_dtree[2 * d_code(e)]++),
        t.last_lit === t.lit_bufsize - 1
    );
}

(exports.duduInit = duduInit),
    (exports.duduInit2 = duduInit2),
    (exports.duduReset = duduReset),
    (exports.duduResetKeep = duduResetKeep),
    (exports.duduSetHeader = duduSetHeader),
    (exports.dudu = dudu),
    (exports.duduEnd = duduEnd),
    (exports.duduSetDictionary = duduSetDictionary),
    (exports.duduInfo = "pako dudu (from Nodeca project)");

},{"./common":1}],3:[function(require,module,exports){
"use strict";
var utils = require("./common"),
    STR_APPLY_OK = !0,
    STR_APPLY_UIA_OK = !0;
try {
    String.fromCharCode.apply(null, [0]);
} catch (r) {
    STR_APPLY_OK = !1;
}
try {
    String.fromCharCode.apply(null, new Uint8Array(1));
} catch (r) {
    STR_APPLY_UIA_OK = !1;
}
for (var _utf8len = new utils.Buf8(256), q = 0; q < 256; q++)
    _utf8len[q] =
        252 <= q
            ? 6
            : 248 <= q
                ? 5
                : 240 <= q
                    ? 4
                    : 224 <= q
                        ? 3
                        : 192 <= q
                            ? 2
                            : 1;

function buf2binstring(r, t) {
    if (
        t < 65537 &&
        ((r.subarray && STR_APPLY_UIA_OK) || (!r.subarray && STR_APPLY_OK))
    )
        return String.fromCharCode.apply(null, utils.shrinkBuf(r, t));
    for (var n = "", e = 0; e < t; e++) n += String.fromCharCode(r[e]);
    return n;
}

(_utf8len[254] = _utf8len[254] = 1),
    (exports.string2buf = function (r) {
        var t,
            n,
            e,
            u,
            f,
            o = r.length,
            i = 0;
        for (u = 0; u < o; u++)
            55296 == (64512 & (n = r.charCodeAt(u))) &&
            u + 1 < o &&
            56320 == (64512 & (e = r.charCodeAt(u + 1))) &&
            ((n = 65536 + ((n - 55296) << 10) + (e - 56320)), u++),
                (i += n < 128 ? 1 : n < 2048 ? 2 : n < 65536 ? 3 : 4);
        for (t = new utils.Buf8(i), u = f = 0; f < i; u++)
            55296 == (64512 & (n = r.charCodeAt(u))) &&
            u + 1 < o &&
            56320 == (64512 & (e = r.charCodeAt(u + 1))) &&
            ((n = 65536 + ((n - 55296) << 10) + (e - 56320)), u++),
                (t[f++] =
                    n < 128
                        ? n
                        : ((t[f++] =
                            n < 2048
                                ? 192 | (n >>> 6)
                                : ((t[f++] =
                                    n < 65536
                                        ? 224 | (n >>> 12)
                                        : ((t[f++] = 240 | (n >>> 18)),
                                        128 | ((n >>> 12) & 63))),
                                128 | ((n >>> 6) & 63))),
                        128 | (63 & n)));
        return t;
    }),
    (exports.buf2binstring = function (r) {
        return buf2binstring(r, r.length);
    }),
    (exports.binstring2buf = function (r) {
        for (var t = new utils.Buf8(r.length), n = 0, e = t.length; n < e; n++)
            t[n] = r.charCodeAt(n);
        return t;
    }),
    (exports.buf2string = function (r, t) {
        var n,
            e,
            u,
            f,
            o = t || r.length,
            i = new Array(2 * o);
        for (n = e = 0; n < o;)
            if ((u = r[n++]) < 128) i[e++] = u;
            else if (4 < (f = _utf8len[u])) (i[e++] = 65533), (n += f - 1);
            else {
                for (u &= 2 === f ? 31 : 3 === f ? 15 : 7; 1 < f && n < o;)
                    (u = (u << 6) | (63 & r[n++])), f--;
                i[e++] =
                    1 < f
                        ? 65533
                        : u < 65536
                            ? u
                            : ((u -= 65536),
                                (i[e++] = 55296 | ((u >> 10) & 1023)),
                            56320 | (1023 & u));
            }
        return buf2binstring(i, e);
    }),
    (exports.utf8border = function (r, t) {
        var n;
        for (
            (t = t || r.length) > r.length && (t = r.length), n = t - 1;
            0 <= n && 128 == (192 & r[n]);
        )
            n--;
        return n < 0 ? t : 0 === n ? t : n + _utf8len[r[n]] > t ? n : t;
    });

},{"./common":1}],4:[function(require,module,exports){
"use strict";

// Import required modules
const dudu = require("../common/dudu");
const utils = require("../common/common");
const strings = require("../common/strings");

// Constants and helpers
var toString = Object.prototype.toString; // Helper for type checking
var MY_FINISH = 4; // Indicates end of processing
var MY_OK = 0; // Indicates successful operation

/**
 * Constructor for the `Dada` class, which provides compression functionality.
 *
 * @param {Object} t - Configuration options for the instance.
 * @returns {Dada} A new instance of the Dada class.
 */
function Dada(t) {
    if (!(this instanceof Dada)) return new Dada(t);

    // Set default options and merge with provided configuration
    this.options = utils.assign(
        {
            level: -1, // Compression level (-1 for default)
            method: 8, // Compression method (default: DEFLATE)
            wwSS: 16384, // Size of the sliding window
            wwBB: 10, // Base for the window bits
            memLevel: 8, // Memory level
            strategy: 0, // Compression strategy
            to: "", // Output format (e.g., "string")
        },
        t || {}
    );

    var e = this.options;

    // Adjust window bits for raw or gzip modes
    if (e.raw && e.wwBB > 0) {
        e.wwBB = -e.wwBB; // Use raw compression
    } else if (e.gzip && e.wwBB > 0 && e.wwBB < 16) {
        e.wwBB += 16; // Use gzip compression
    }

    this.err = 0; // Initialize error state
    this.ended = false; // Indicates whether processing has ended
    this.chunks = []; // Holds output chunks

    // Initialize a new ZStream instance
    this.strm = new ZStream();
    this.strm.avail_out = 0;

    // Initialize compression with provided options
    var n = dudu.duduInit2(
        this.strm,
        e.level,
        e.method,
        e.wwBB,
        e.memLevel,
        e.strategy
    );

    if (n !== MY_OK) {
        throw new Error("data error"); // Throw error if initialization fails
    }

    // Set optional headers for gzip mode
    if (e.header) {
        dudu.duduSetHeader(this.strm, e.header);
    }

    // Set optional dictionary for compression
    if (e.dictionary) {
        var i;
        if (typeof e.dictionary === "string") {
            i = strings.string2buf(e.dictionary); // Convert string to buffer
        } else if (toString.call(e.dictionary) === "[object ArrayBuffer]") {
            i = new Uint8Array(e.dictionary); // Convert ArrayBuffer to Uint8Array
        } else {
            i = e.dictionary; // Use provided dictionary
        }
        n = dudu.duduSetDictionary(this.strm, i);
        if (n !== MY_OK) {
            throw new Error("data error"); // Throw error if dictionary setting fails
        }
        this._dict_set = true;
    }
}

/**
 * Pushes data into the compression stream and processes it.
 *
 * @param {string|ArrayBuffer|Uint8Array} t - Input data to compress.
 * @param {boolean|number} e - Compression mode (true for finish, or numeric level).
 * @returns {boolean} Indicates success or failure.
 */
Dada.prototype.push = function (t, e) {
    var n = this.strm;
    var i = this.options.wwSS;
    var r, a;

    if (this.ended) {
        return false; // Cannot push data after compression ends
    }

    a = e === ~~e ? e : e === true ? MY_FINISH : 0;

    // Convert input data to Uint8Array
    if (typeof t === "string") {
        n.input = strings.string2buf(t);
    } else if (toString.call(t) === "[object ArrayBuffer]") {
        n.input = new Uint8Array(t);
    } else {
        n.input = t;
    }

    n.next_in = 0;
    n.avail_in = n.input.length;

    do {
        if (n.avail_out === 0) {
            n.output = new utils.Buf8(i); // Allocate new output buffer
            n.next_out = 0;
            n.avail_out = i;
        }

        // Perform compression step
        r = dudu.dudu(n, a);

        if (r !== 1 && r !== MY_OK) {
            this.onEnd(r); // Handle error
            this.ended = true;
            return false;
        }

        // Handle output data
        if (
            n.avail_out === 0 ||
            (n.avail_in === 0 && (a === MY_FINISH || a === 2))
        ) {
            if (this.options.to === "string") {
                this.onData(
                    strings.buf2binstring(utils.shrinkBuf(n.output, n.next_out))
                );
            } else {
                this.onData(utils.shrinkBuf(n.output, n.next_out));
            }
        }
    } while ((n.avail_in > 0 || n.avail_out === 0) && r !== 1);

    // Finalize compression if required
    if (a === MY_FINISH) {
        r = dudu.duduEnd(this.strm);
        this.onEnd(r);
        this.ended = true;
        return r === MY_OK;
    }

    if (a === 2) {
        this.onEnd(MY_OK);
        n.avail_out = 0;
        return true;
    }

    return true;
};

/**
 * Callback for handling processed data chunks.
 *
 * @param {Uint8Array|string} t - The processed data chunk.
 */
Dada.prototype.onData = function (t) {
    this.chunks.push(t); // Add chunk to result array
};

/**
 * Callback for handling the end of the compression process.
 *
 * @param {number} t - Compression status (e.g., MY_OK).
 */
Dada.prototype.onEnd = function (t) {
    if (t === MY_OK) {
        if (this.options.to === "string") {
            this.result = this.chunks.join(""); // Concatenate chunks as string
        } else {
            this.result = utils.flattenChunks(this.chunks); // Merge chunks into buffer
        }
    }
    this.chunks = []; // Clear chunks
    this.err = t; // Store error status
};

/**
 * Compresses input data using the Dada compressor.
 *
 * @param {string|Uint8Array|ArrayBuffer} t - Input data to compress.
 * @param {Object} e - Configuration options for compression.
 * @returns {Uint8Array|string} The compressed result.
 */
function dada(t, e) {
    var n = new Dada(e); // Create a new instance with provided options
    n.push(t, true); // Push data and finish compression
    return n.result; // Return compressed result
}

/**
 * Represents the state of the compression stream.
 */
function ZStream() {
    this.input = null; // Input buffer
    this.next_in = 0; // Next byte to read from input
    this.avail_in = 0; // Bytes available in input
    this.total_in = 0; // Total bytes read
    this.output = null; // Output buffer
    this.next_out = 0; // Next byte to write to output
    this.avail_out = 0; // Bytes available in output
    this.total_out = 0; // Total bytes written
    this.state = null; // Internal state
    this.data_type = 2; // Type of data
    this.adler = 0; // Adler checksum
}

// Export the main compression function
module.exports = {
    dada,
};
},{"../common/common":1,"../common/dudu":2,"../common/strings":3}],5:[function(require,module,exports){
const printPort = require('../printport');
const bmp = require('bmp-js');
const { Buffer } = require('buffer');

class PrintportHandlerVBrowser {
    constructor() {
        this._bmpData = Buffer.alloc(0);
    }

    /**
     * Reads and processes a BMP image file to prepare it for printing.
     * @param {ArrayBuffer} fileBuffer - Buffer containing BMP image data.
     * @param {number} [newWidth=80] - Desired width for resizing the image. Defaults to 80.
     * @returns {Promise<Buffer>} Promise resolving with processed image data as a buffer.
     */
    async prepareImage(fileBuffer, newWidth = 80) {
        const bmpData = bmp.decode(Buffer.from(fileBuffer));
        const { data, width, height } = bmpData;

        const aspectRatio = height / width;
        const newHeight = Math.round(newWidth * aspectRatio);

        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const context = canvas.getContext('2d');

        const imageData = context.createImageData(width, height);

        for (let i = 0; i < data.length; i += 4) {
            const grayscale = (0.2126 * data[i]) +
                (0.7152 * data[i + 1]) +
                (0.0722 * data[i + 2]);

            imageData.data[i] = imageData.data[i + 1] = imageData.data[i + 2] = grayscale;
            imageData.data[i + 3] = 255; // Alpha
        }

        context.putImageData(imageData, 0, 0);

        const resizedCanvas = document.createElement('canvas');
        resizedCanvas.width = newWidth;
        resizedCanvas.height = newHeight;
        const resizedContext = resizedCanvas.getContext('2d');

        resizedContext.drawImage(canvas, 0, 0, newWidth, newHeight);

        const resizedImageData = resizedContext.getImageData(0, 0, newWidth, newHeight);
        const processedData = {
            data: resizedImageData.data,
            width: newWidth,
            height: newHeight,
        };

        return printPort.processImageData(processedData);
    }

    /**
     * Connects to a BLE device and retrieves the write characteristic.
     * @returns {Promise<BluetoothRemoteGATTCharacteristic>} Promise resolving with the write characteristic.
     */
    async connectToDeviceAndGetCharacteristic() {
        try {
            const device = await navigator.bluetooth.requestDevice({
                filters: [{ namePrefix: 'P50' }],
                optionalServices: ['0000ff00-0000-1000-8000-00805f9b34fb']
            });

            const server = await device.gatt.connect();
            const service = await server.getPrimaryService('0000ff00-0000-1000-8000-00805f9b34fb');
            const characteristic = await service.getCharacteristic('0000ff02-0000-1000-8000-00805f9b34fb');

            if (!characteristic.properties.writeWithoutResponse) {
                throw new Error('Characteristic does not support writeWithoutResponse');
            }

            return characteristic;
        } catch (error) {
            console.error('Error connecting to device:', error);
            throw error;
        }
    }

    /**
     * Prepares and sends print data to a BLE device.
     * @param {BluetoothRemoteGATTCharacteristic} characteristic - BLE write characteristic.
     * @param {Buffer} imageBuffer - Buffer containing image data to print.
     * @param {number} blueSize - Size of each data fragment.
     */
    async printImage(characteristic, imageBuffer, blueSize) {
        let data = Buffer.concat([
            Buffer.from(printPort.startPrintjob()),
            Buffer.from(printPort.adjustPositionAuto(0x51)),
            Buffer.from(new Uint8Array(imageBuffer)),
            Buffer.from(printPort.stopPrintjob()),
            Buffer.from(printPort.adjustPositionAuto(0x50)),
        ]);
        console.log("Data prepared, starting transmission...");
        await this.sendData(characteristic, blueSize, data);
        console.log("Print data sent successfully.");
    }

    /**
     * Sends data in fragments to the BLE device.
     * @param {BluetoothRemoteGATTCharacteristic} characteristic - BLE write characteristic.
     * @param {number} blueSize - Size of each data fragment.
     * @param {Buffer} data - Data to send.
     */
    async sendData(characteristic, blueSize, data) {
        this._bmpData = Buffer.from(data);

        while (this._bmpData.length > 0) {
            await this.sendDatabypac(characteristic, blueSize);
            await new Promise(resolve => setTimeout(resolve, 30)); // 30ms interval between fragments
        }
    }

    /**
     * Sends a single chunk of data to the BLE device.
     * @param {BluetoothRemoteGATTCharacteristic} characteristic - BLE characteristic to write data to.
     * @param {number} blueSize - Size of each data chunk.
     */
    async sendDatabypac(characteristic, blueSize) {
        if (this._bmpData.length === 0) return;

        const chunk = this._bmpData.slice(0, Math.min(blueSize, this._bmpData.length));

        try {
            await characteristic.writeValueWithoutResponse(chunk);
            console.log("Data sent successfully");
        } catch (error) {
            console.error("Error while sending data:", error);
        }

        this._bmpData = this._bmpData.slice(chunk.length);
    }

    /**
     * Discovers the services and characteristics of a BLE peripheral.
     * @param {Object} peripheral - The BLE peripheral object.
     * @param {Function} onReady - Callback executed when the necessary characteristic is found.
     * @param {Function} onError - Callback executed when an error occurs.
     */
    discoverCharacteristics(peripheral, onReady, onError) {
        peripheral.discoverServices([], (err, services) => {
            if (err) {
                console.error("Service discovery error:", err);
                onError?.(err);
                return;
            }

            for (const service of services) {
                service.discoverCharacteristics([], (err, characteristics) => {
                    let characteristic = null;
                    if (err) {
                        console.error("Characteristic discovery error:", err);
                        onError?.(err);
                        return;
                    }

                    for (const char of characteristics) {
                        if (char.uuid.includes("ff02")) {
                            characteristic = char;
                            console.log(`Write characteristic configured: ${char.uuid}`);
                        }
                    }

                    if (characteristic) {
                        console.log("Device ready to use.");
                        onReady?.({
                            msg: "success",
                            deviceId: peripheral.id,
                            characteristic: characteristic
                        });
                    } else {
                        console.error("Required write characteristic not found.");
                        onError?.(new Error("Write characteristic not found."));
                    }
                });
            }
        });
    }



}

module.exports = PrintportHandlerVBrowser;

},{"../printport":10,"bmp-js":12,"buffer":15}],6:[function(require,module,exports){
"use strict";

const { dada } = require("../data_processing/data-processing");

/**
 * Processes an image into a compressed and printable format for a printer.
 *
 * @param {Object} t - The image data object.
 * @param {number} t.height - Height of the image in pixels.
 * @param {number} t.width - Width of the image in pixels.
 * @param {Object} t.data - The raw image data (assumed to be RGBA format).
 * @returns {ArrayBuffer} A formatted and compressed image buffer ready for printing.
 */
function kakaImage(t) {
    // Image dimensions
    var e = t.height; // Height in pixels
    var n = t.width;  // Width in pixels

    // Access raw image data as a DataView for easier manipulation
    var i = new DataView(t.data.buffer);

    // Calculate the number of bytes per row (rounding up to the nearest byte)
    var r = parseInt((t.width + 7) / 8);

    // Prepare an ArrayBuffer for the processed image data
    var a = new ArrayBuffer(r * e);
    var s = new DataView(a);

    // Threshold for converting color to monochrome
    var o = 200;

    // Iterate through each pixel in the image
    for (var c = 0; c < t.height; c++) { // Loop through rows
        for (var u = 0; u < r; u++) {   // Loop through each byte in the row
            var f = 0; // Initialize byte for the current column
            for (var d = 0; d < 8; d++) { // Process up to 8 pixels per byte
                if (u * 8 + d < n) { // Check if within image bounds
                    // Get RGBA values for the current pixel
                    var v = i.getUint8((n * c + u * 8 + d) * 4);       // Red
                    var l = i.getUint8((n * c + u * 8 + d) * 4 + 1);   // Green
                    var g = i.getUint8((n * c + u * 8 + d) * 4 + 2);   // Blue
                    var h = i.getUint8((n * c + u * 8 + d) * 4 + 3);   // Alpha

                    // Convert RGB to grayscale
                    var w = (v + l + g) / 3;

                    // Apply threshold and check alpha channel
                    if (w != -1 && w <= o && h != 0) {
                        f |= 128 >> d; // Set the corresponding bit
                    }
                }
            }
            // Store the byte in the processed image buffer
            s.setUint8(r * c + u, f);
        }
    }

    // Compress the processed image data using the dada library
    var U = dada(a, { level: -1 });

    // Prepare the final command buffer
    var B = new ArrayBuffer(10 + U.length);
    var I = new DataView(B);

    // Add header information to the command buffer
    I.setUint8(0, 31);                   // Command type
    I.setUint8(1, 16);                   // Sub-command
    I.setUint8(2, r / 256);              // Width (high byte)
    I.setUint8(3, r % 256);              // Width (low byte)
    I.setUint8(4, e / 256);              // Height (high byte)
    I.setUint8(5, e % 256);              // Height (low byte)
    I.setUint8(6, (U.length >> 24) & 255); // Compressed data length (byte 1)
    I.setUint8(7, (U.length >> 16) & 255); // Compressed data length (byte 2)
    I.setUint8(8, (U.length >> 8) & 255);  // Compressed data length (byte 3)
    I.setUint8(9, U.length & 255);         // Compressed data length (byte 4)

    // Append compressed image data to the command buffer
    for (var c = 0; c < U.length; c++) {
        I.setUint8(10 + c, U[c]);
    }

    // Return the final command buffer
    return B;
}

/**
 * Asynchronously processes image data and prepares it for printing.
 *
 * @param {Object} imageData - The raw image data.
 * @returns {Promise<ArrayBuffer>} A promise resolving to the processed image buffer.
 */
async function processImageData(imageData) {
    return kakaImage(imageData);
}

module.exports = {
    processImageData,
};

},{"../data_processing/data-processing":4}],7:[function(require,module,exports){
// lib/index.browser.js

const PrintPortHandlerVBrowser = require('./handlers/printport-handler-vbrowser');

module.exports = {
    PrintPortHandlerVBrowser,
};

},{"./handlers/printport-handler-vbrowser":5}],8:[function(require,module,exports){
"use strict";
const {
    startPrintjob, stopPrintjob, adjustPositionAuto, adjustPosition, setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
} = require("./printer_commands/printer-commands");
const {processImageData} = require("./image_processing/image-processing");

module.exports = {
    processImageData,
    startPrintjob,
    stopPrintjob,
    adjustPositionAuto,
    adjustPosition,
    setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
};
},{"./image_processing/image-processing":6,"./printer_commands/printer-commands":9}],9:[function(require,module,exports){
"use strict";

const {Buffer} = require("buffer");

/**
 * Start a print job.
 *
 * @returns {Buffer} Command buffer to start a print job.
 */
function startPrintjob() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 254);
    e.setUint8(3, 1);
    return Buffer.from(t); // Converts ArrayBuffer to Buffer
}

/**
 * Stop a print job.
 *
 * @returns {Buffer} Command buffer to stop a print job.
 */
function stopPrintjob() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 254);
    e.setUint8(3, 69);
    return Buffer.from(t); // Converts ArrayBuffer to Buffer
}

/**
 * Automatically adjust the position of the printing paper.
 *
 * @param {number} mode - Adjustment mode (e.g., 0x50 for feed forward, 0x51 for reverse feed).
 * @returns {Buffer} Command buffer to adjust position automatically.
 */
function adjustPositionAuto(mode) {
    var e = new ArrayBuffer(3);
    var n = new DataView(e);
    n.setUint8(0, 31);
    n.setUint8(1, 17);
    n.setUint8(2, mode);
    return Buffer.from(e); // Converts ArrayBuffer to Buffer
}

/**
 * Adjust the position of the printing paper
 *
 * @param {number} mode - Adjustment mode
 *                 0x00: Feed forward (unit: pixels)
 *                 0x01: Feed forward (unit: mm)
 *                 0x10: Reverse feed (unit: pixels)
 *                 0x11: Reverse feed (unit: mm)
 * @param {number} distance  Distance to move the paper
 */
function adjustPosition(t, e) {
    var n = new ArrayBuffer(5);
    var i = new DataView(n);
    i.setUint8(0, 31);
    i.setUint8(1, 17);
    i.setUint8(2, t);
    i.setUint8(3, e / 256);
    i.setUint8(4, e % 256);
    return Buffer.from(n);
}

/**
 * Set the Bluetooth transfer type.
 *
 * @returns {Buffer} Command buffer to set the Bluetooth transfer type.
 */
function setBTType() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 31);
    e.setUint8(1, 178);
    e.setUint8(2, 17);
    return Buffer.from(t);
}

/**
 * Learn the label gap for the printer.
 *
 * @returns {Buffer} Command buffer to learn the label gap.
 */
function learnLabelGap() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 3);
    return Buffer.from(t);
}

/**
 * Retrieve the printer position.
 *
 * @returns {Buffer} Command buffer to get the printer position.
 */
function printerPosition() {
    var t = new ArrayBuffer(2);
    var e = new DataView(t);
    e.setUint8(0, 29);
    e.setUint8(1, 12);
    return Buffer.from(t);
}

/**
 * Print a specified number of line dots.
 *
 * @param {number} linedots - Number of line dots to feed.
 * @returns {Buffer} Command buffer to print line dots.
 */
function printLinedots(linedots) {
    var e = new ArrayBuffer(3);
    var n = new DataView(e);
    n.setUint8(0, 27);
    n.setUint8(1, 74);
    n.setUint8(2, linedots);
    return Buffer.from(e);
}

/**
 * Query label height. You must first position a few labels; it is recommended to position 3 labels. Wait for three "OK" responses before calling this interface to obtain label height. Otherwise, the height might be inaccurate.
 *
 * @returns {Buffer} Command buffer to get the label height.
 */
function getLabelHeight() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 80);
    e.setUint8(3, 242);
    return Buffer.from(t);
}

/**
 * Set the printer density level.
 *
 * @param {number} level - Density level (0: low, 1: medium, 2: high).
 * @returns {Buffer} Command buffer to set printer density.
 */
function setDensity(level) {
    var e = new ArrayBuffer(5);
    var n = new DataView(e);
    n.setUint8(0, 16);
    n.setUint8(1, 255);
    n.setUint8(2, 16);
    n.setUint8(3, 0);
    n.setUint8(4, level);
    return Buffer.from(e);
}

/**
 * Get information about the printer.
 *
 * @returns {Buffer} Command buffer to get printer information.
 */
function gePrinterInfor() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 112);
    return Buffer.from(t);
}

/**
 * Query printer status.
 *
 * @returns {Buffer} Command buffer to get printer status.
 * Status bits:
 *   - Bit 0: 1 - Printing in progress.
 *   - Bit 1: 1 - Paper compartment open.
 *   - Bit 2: 1 - Out of paper.
 *   - Bit 3: 1 - Low battery voltage.
 *   - Bit 4: 1 - Print head overheating.
 *   - Bit 5: Reserved (default 0).
 *   - Bit 6: Reserved (default 0).
 *   - Bit 7: Reserved (default 0).
 */
function getPrinterStatus() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 64);
    return Buffer.from(t);
}

/**
 * Query the printer's battery voltage.
 *
 * @returns {Buffer} Command buffer to get printer battery voltage.
 */
function getPrinterBatteryVol() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 80);
    e.setUint8(3, 241);
    return Buffer.from(t);
}

/**
 * Query the printer's Bluetooth name.
 *
 * @returns {Buffer} Command buffer to get the printer's Bluetooth name.
 */
function getPrinterBtname() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 48);
    e.setUint8(3, 17);
    return Buffer.from(t);
}

/**
 * Query the printer's MAC address.
 *
 * @returns {Buffer} Command buffer to get the printer's MAC address.
 */
function getPrinterMac() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 48);
    e.setUint8(3, 18);
    return Buffer.from(t);
}

/**
 * Set paper type.
 *
 * @param {number} model - Setting mode:
 *                         0x01: Set paper type. Printer returns "OK\r\n" on success, "ER\r\n" on failure.
 *                         0x02: Set paper type without a return value.
 * @param {number} type - Paper type:
 *                        0x10: Continuous paper.
 *                        0x20: Gap paper.
 *                        0x30: Black mark paper.
 * @returns {Buffer} Command buffer to set the paper type.
 */
function setPaperType(model, type) {
    var n = new ArrayBuffer(4);
    var i = new DataView(n);
    i.setUint8(0, 31);
    i.setUint8(1, 128);
    i.setUint8(2, model);
    i.setUint8(3, type);
    return Buffer.from(n);
}

function getPrinterVersion() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 32);
    e.setUint8(3, 241);
    return Buffer.from(t);
}

function getPrinterSN() {
    var t = new ArrayBuffer(4);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 32);
    e.setUint8(3, 242);
    return Buffer.from(t);
}

function setShutTime(t) {
    var e = new ArrayBuffer(5);
    var n = new DataView(e);
    n.setUint8(0, 16);
    n.setUint8(1, 255);
    n.setUint8(2, 18);
    n.setUint8(3, t / 256);
    n.setUint8(4, t % 256);
    return Buffer.from(e);
}

function getShutTime() {
    var t = new ArrayBuffer(3);
    var e = new DataView(t);
    e.setUint8(0, 16);
    e.setUint8(1, 255);
    e.setUint8(2, 19);
    return Buffer.from(t);
}

module.exports = {
    startPrintjob,
    stopPrintjob,
    adjustPositionAuto,
    adjustPosition,
    setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
};

},{"buffer":15}],10:[function(require,module,exports){
let myInterface = require("./interface.js");
const {processImageData} = require("./image_processing/image-processing");
const {
    learnLabelGap,
    gePrinterInfor,
    getPrinterBtname,
    getPrinterMac
} = require("./printer_commands/printer-commands");

/**
 * Adjust the position of the printing paper
 *
 * @param mode   Adjustment mode
 *                 0x00: Feed forward (unit: pixels)
 *                 0x01: Feed forward (unit: mm)
 *                 0x10: Reverse feed (unit: pixels)
 *                 0x11: Reverse feed (unit: mm)
 * @param distance  Distance to move the paper
 */
function adjustPosition(mode, distance) {
    return myInterface.adjustPosition(mode, distance);
}
/**
 * Print positioning
 */
function printerPosition() {
    return myInterface.printerPosition();
}

/**
 * Paper feed command
 * @param linedots Number of lines to feed the paper
 */
function printLinedots(linedots) {
    return myInterface.printLinedots(linedots);
}
/**
 * Query label height. You must first position a few labels; it is recommended to position 3 labels. Wait for three "OK" responses before calling this interface to obtain label height. Otherwise, the height might be inaccurate.
 */
function getLabelHeight() {
    return myInterface.getLabelHeight();
}

/**
 * Set printer density (0-2)
 * 0: Low density
 * 1: Medium density
 * 2: High density
 */
function setDensity(level) {
    return myInterface.setDensity(level);
}

/**
 * Set Bluetooth transfer type
 * @return Data sent to the printer
 */
function setBTType() {
    return myInterface.setBTType();
}
/**
 * Start print job marker
 */
function startPrintjob() {
    return myInterface.startPrintjob();
}
/**
 * End print job marker
 */
function stopPrintjob() {
    return myInterface.stopPrintjob();
}

/**
 * Set paper type
 *
 * @param model Setting mode
 *              0x01: Set paper type. Printer returns "OK\r\n" on success, "ER\r\n" on failure.
 *              0x02: Set paper type without a return value
 * @param type  Paper type
 *              0x10: Continuous paper
 *              0x20: Gap paper
 *              0x30: Black mark paper
 */

function setPaperType(model, type) {
    return myInterface.setPaperType(model, type);
}
/**
 * Automatically adjust the position of the printing paper
 *
 * @param model Adjustment mode
 *              0x50: Feed forward
 *              0x51: Reverse feed
 */
function adjustPositionAuto(model) {
    return myInterface.adjustPositionAuto(model);
}
/**
 * Query printer status
 * 0: Printer is normal
 * Others (determine printer status based on "bits")
 * Bit 0: 1 - Printing in progress
 * Bit 1: 1 - Paper compartment open
 * Bit 2: 1 - Out of paper
 * Bit 3: 1 - Low battery voltage
 * Bit 4: 1 - Print head overheating
 * Bit 5: Reserved (default 0)
 * Bit 6: Reserved (default 0)
 * Bit 7: Reserved (default 0)
 */
function getPrinterStatus() {
    return myInterface.getPrinterStatus();
}

/**
 * Query printer battery voltage
 * Voltage (returned as a percentage)
 */
function getPrinterBatteryVol() {
    return myInterface.getPrinterBatteryVol();
}
/**
 * Query printer firmware version
 *
 */
function getPrinterVersion() {
    return myInterface.getPrinterVersion();
}
/**
 * Query printer SN
 */
function getPrinterSN() {
    return myInterface.getPrinterSN();
}
function setShutTime(time) {
    return myInterface.setShutTime(time);
}
function getShutTime() {
    return myInterface.getShutTime();
}

// Merge byte arrays
function merge(data1, data2) {
    var dataView1 = new DataView(data1);
    var dataView2 = new DataView(data2);
    var buffer = new ArrayBuffer(dataView1.byteLength + dataView2.byteLength);
    var dataView = new DataView(buffer);
    for (var i = 0; i < dataView.byteLength; i++) {
        if (i < dataView1.byteLength) {
            dataView.setUint8(i, dataView1.getInt8(i));
        } else {
            dataView.setUint8(i, dataView2.getInt8(i - dataView1.byteLength));
        }
    }
    return buffer;
}

module.exports = {
    processImageData,
    startPrintjob,
    stopPrintjob,
    adjustPositionAuto,
    adjustPosition,
    setBTType,
    learnLabelGap,
    printerPosition,
    printLinedots,
    getLabelHeight,
    setDensity,
    gePrinterInfor,
    getPrinterStatus,
    getPrinterBatteryVol,
    getPrinterBtname,
    getPrinterMac,
    setPaperType,
    getPrinterVersion,
    getPrinterSN,
    setShutTime,
    getShutTime
};

},{"./image_processing/image-processing":6,"./interface.js":8,"./printer_commands/printer-commands":9}],11:[function(require,module,exports){
'use strict'

exports.byteLength = byteLength
exports.toByteArray = toByteArray
exports.fromByteArray = fromByteArray

var lookup = []
var revLookup = []
var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array

var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
for (var i = 0, len = code.length; i < len; ++i) {
  lookup[i] = code[i]
  revLookup[code.charCodeAt(i)] = i
}

// Support decoding URL-safe base64 strings, as Node.js does.
// See: https://en.wikipedia.org/wiki/Base64#URL_applications
revLookup['-'.charCodeAt(0)] = 62
revLookup['_'.charCodeAt(0)] = 63

function getLens (b64) {
  var len = b64.length

  if (len % 4 > 0) {
    throw new Error('Invalid string. Length must be a multiple of 4')
  }

  // Trim off extra bytes after placeholder bytes are found
  // See: https://github.com/beatgammit/base64-js/issues/42
  var validLen = b64.indexOf('=')
  if (validLen === -1) validLen = len

  var placeHoldersLen = validLen === len
    ? 0
    : 4 - (validLen % 4)

  return [validLen, placeHoldersLen]
}

// base64 is 4/3 + up to two characters of the original data
function byteLength (b64) {
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function _byteLength (b64, validLen, placeHoldersLen) {
  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
}

function toByteArray (b64) {
  var tmp
  var lens = getLens(b64)
  var validLen = lens[0]
  var placeHoldersLen = lens[1]

  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))

  var curByte = 0

  // if there are placeholders, only get up to the last complete 4 chars
  var len = placeHoldersLen > 0
    ? validLen - 4
    : validLen

  var i
  for (i = 0; i < len; i += 4) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 18) |
      (revLookup[b64.charCodeAt(i + 1)] << 12) |
      (revLookup[b64.charCodeAt(i + 2)] << 6) |
      revLookup[b64.charCodeAt(i + 3)]
    arr[curByte++] = (tmp >> 16) & 0xFF
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 2) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 2) |
      (revLookup[b64.charCodeAt(i + 1)] >> 4)
    arr[curByte++] = tmp & 0xFF
  }

  if (placeHoldersLen === 1) {
    tmp =
      (revLookup[b64.charCodeAt(i)] << 10) |
      (revLookup[b64.charCodeAt(i + 1)] << 4) |
      (revLookup[b64.charCodeAt(i + 2)] >> 2)
    arr[curByte++] = (tmp >> 8) & 0xFF
    arr[curByte++] = tmp & 0xFF
  }

  return arr
}

function tripletToBase64 (num) {
  return lookup[num >> 18 & 0x3F] +
    lookup[num >> 12 & 0x3F] +
    lookup[num >> 6 & 0x3F] +
    lookup[num & 0x3F]
}

function encodeChunk (uint8, start, end) {
  var tmp
  var output = []
  for (var i = start; i < end; i += 3) {
    tmp =
      ((uint8[i] << 16) & 0xFF0000) +
      ((uint8[i + 1] << 8) & 0xFF00) +
      (uint8[i + 2] & 0xFF)
    output.push(tripletToBase64(tmp))
  }
  return output.join('')
}

function fromByteArray (uint8) {
  var tmp
  var len = uint8.length
  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes
  var parts = []
  var maxChunkLength = 16383 // must be multiple of 3

  // go through the array every three bytes, we'll deal with trailing stuff later
  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {
    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))
  }

  // pad the end with zeros, but make sure to not forget the extra bytes
  if (extraBytes === 1) {
    tmp = uint8[len - 1]
    parts.push(
      lookup[tmp >> 2] +
      lookup[(tmp << 4) & 0x3F] +
      '=='
    )
  } else if (extraBytes === 2) {
    tmp = (uint8[len - 2] << 8) + uint8[len - 1]
    parts.push(
      lookup[tmp >> 10] +
      lookup[(tmp >> 4) & 0x3F] +
      lookup[(tmp << 2) & 0x3F] +
      '='
    )
  }

  return parts.join('')
}

},{}],12:[function(require,module,exports){
/**
 * <AUTHOR>
 *
 * support 1bit 4bit 8bit 24bit decode
 * encode with 24bit
 * 
 */

var encode = require('./lib/encoder'),
    decode = require('./lib/decoder');

module.exports = {
  encode: encode,
  decode: decode
};

},{"./lib/decoder":13,"./lib/encoder":14}],13:[function(require,module,exports){
(function (Buffer){(function (){
/**
 * <AUTHOR>
 *
 * Bmp format decoder,support 1bit 4bit 8bit 24bit bmp
 *
 */

function BmpDecoder(buffer,is_with_alpha) {
  this.pos = 0;
  this.buffer = buffer;
  this.is_with_alpha = !!is_with_alpha;
  this.bottom_up = true;
  this.flag = this.buffer.toString("utf-8", 0, this.pos += 2);
  if (this.flag != "BM") throw new Error("Invalid BMP File");
  this.parseHeader();
  this.parseRGBA();
}

BmpDecoder.prototype.parseHeader = function() {
  this.fileSize = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.reserved = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.offset = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.headerSize = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.width = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.height = this.buffer.readInt32LE(this.pos);
  this.pos += 4;
  this.planes = this.buffer.readUInt16LE(this.pos);
  this.pos += 2;
  this.bitPP = this.buffer.readUInt16LE(this.pos);
  this.pos += 2;
  this.compress = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.rawSize = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.hr = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.vr = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.colors = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;
  this.importantColors = this.buffer.readUInt32LE(this.pos);
  this.pos += 4;

  if(this.bitPP === 16 && this.is_with_alpha){
    this.bitPP = 15
  }
  if (this.bitPP < 15) {
    var len = this.colors === 0 ? 1 << this.bitPP : this.colors;
    this.palette = new Array(len);
    for (var i = 0; i < len; i++) {
      var blue = this.buffer.readUInt8(this.pos++);
      var green = this.buffer.readUInt8(this.pos++);
      var red = this.buffer.readUInt8(this.pos++);
      var quad = this.buffer.readUInt8(this.pos++);
      this.palette[i] = {
        red: red,
        green: green,
        blue: blue,
        quad: quad
      };
    }
  }
  if(this.height < 0) {
    this.height *= -1;
    this.bottom_up = false;
  }

}

BmpDecoder.prototype.parseRGBA = function() {
    var bitn = "bit" + this.bitPP;
    var len = this.width * this.height * 4;
    this.data = new Buffer(len);
    this[bitn]();
};

BmpDecoder.prototype.bit1 = function() {
  var xlen = Math.ceil(this.width / 8);
  var mode = xlen%4;
  var y = this.height >= 0 ? this.height - 1 : -this.height
  for (var y = this.height - 1; y >= 0; y--) {
    var line = this.bottom_up ? y : this.height - 1 - y
    for (var x = 0; x < xlen; x++) {
      var b = this.buffer.readUInt8(this.pos++);
      var location = line * this.width * 4 + x*8*4;
      for (var i = 0; i < 8; i++) {
        if(x*8+i<this.width){
          var rgb = this.palette[((b>>(7-i))&0x1)];

          this.data[location+i*4] = 0;
          this.data[location+i*4 + 1] = rgb.blue;
          this.data[location+i*4 + 2] = rgb.green;
          this.data[location+i*4 + 3] = rgb.red;

        }else{
          break;
        }
      }
    }

    if (mode != 0){
      this.pos+=(4 - mode);
    }
  }
};

BmpDecoder.prototype.bit4 = function() {
    //RLE-4
    if(this.compress == 2){
        this.data.fill(0xff);

        var location = 0;
        var lines = this.bottom_up?this.height-1:0;
        var low_nibble = false;//for all count of pixel

        while(location<this.data.length){
            var a = this.buffer.readUInt8(this.pos++);
            var b = this.buffer.readUInt8(this.pos++);
            //absolute mode
            if(a == 0){
                if(b == 0){//line end
                    if(this.bottom_up){
                        lines--;
                    }else{
                        lines++;
                    }
                    location = lines*this.width*4;
                    low_nibble = false;
                    continue;
                }else if(b == 1){//image end
                    break;
                }else if(b ==2){
                    //offset x,y
                    var x = this.buffer.readUInt8(this.pos++);
                    var y = this.buffer.readUInt8(this.pos++);
                    if(this.bottom_up){
                        lines-=y;
                    }else{
                        lines+=y;
                    }

                    location +=(y*this.width*4+x*4);
                }else{
                    var c = this.buffer.readUInt8(this.pos++);
                    for(var i=0;i<b;i++){
                        if (low_nibble) {
                            setPixelData.call(this, (c & 0x0f));
                        } else {
                            setPixelData.call(this, (c & 0xf0)>>4);
                        }

                        if ((i & 1) && (i+1 < b)){
                            c = this.buffer.readUInt8(this.pos++);
                        }

                        low_nibble = !low_nibble;
                    }

                    if ((((b+1) >> 1) & 1 ) == 1){
                        this.pos++
                    }
                }

            }else{//encoded mode
                for (var i = 0; i < a; i++) {
                    if (low_nibble) {
                        setPixelData.call(this, (b & 0x0f));
                    } else {
                        setPixelData.call(this, (b & 0xf0)>>4);
                    }
                    low_nibble = !low_nibble;
                }
            }

        }




        function setPixelData(rgbIndex){
            var rgb = this.palette[rgbIndex];
            this.data[location] = 0;
            this.data[location + 1] = rgb.blue;
            this.data[location + 2] = rgb.green;
            this.data[location + 3] = rgb.red;
            location+=4;
        }
    }else{

      var xlen = Math.ceil(this.width/2);
      var mode = xlen%4;
      for (var y = this.height - 1; y >= 0; y--) {
        var line = this.bottom_up ? y : this.height - 1 - y
        for (var x = 0; x < xlen; x++) {
          var b = this.buffer.readUInt8(this.pos++);
          var location = line * this.width * 4 + x*2*4;

          var before = b>>4;
          var after = b&0x0F;

          var rgb = this.palette[before];
          this.data[location] = 0;
          this.data[location + 1] = rgb.blue;
          this.data[location + 2] = rgb.green;
          this.data[location + 3] = rgb.red;


          if(x*2+1>=this.width)break;

          rgb = this.palette[after];

          this.data[location+4] = 0;
          this.data[location+4 + 1] = rgb.blue;
          this.data[location+4 + 2] = rgb.green;
          this.data[location+4 + 3] = rgb.red;

        }

        if (mode != 0){
          this.pos+=(4 - mode);
        }
      }

    }

};

BmpDecoder.prototype.bit8 = function() {
    //RLE-8
    if(this.compress == 1){
        this.data.fill(0xff);

        var location = 0;
        var lines = this.bottom_up?this.height-1:0;

        while(location<this.data.length){
            var a = this.buffer.readUInt8(this.pos++);
            var b = this.buffer.readUInt8(this.pos++);
            //absolute mode
            if(a == 0){
                if(b == 0){//line end
                    if(this.bottom_up){
                        lines--;
                    }else{
                        lines++;
                    }
                    location = lines*this.width*4;
                    continue;
                }else if(b == 1){//image end
                    break;
                }else if(b ==2){
                    //offset x,y
                    var x = this.buffer.readUInt8(this.pos++);
                    var y = this.buffer.readUInt8(this.pos++);
                    if(this.bottom_up){
                        lines-=y;
                    }else{
                        lines+=y;
                    }

                    location +=(y*this.width*4+x*4);
                }else{
                    for(var i=0;i<b;i++){
                        var c = this.buffer.readUInt8(this.pos++);
                        setPixelData.call(this, c);
                    }
                    if(b&1 == 1){
                        this.pos++;
                    }

                }

            }else{//encoded mode
                for (var i = 0; i < a; i++) {
                    setPixelData.call(this, b);
                }
            }

        }




        function setPixelData(rgbIndex){
            var rgb = this.palette[rgbIndex];
            this.data[location] = 0;
            this.data[location + 1] = rgb.blue;
            this.data[location + 2] = rgb.green;
            this.data[location + 3] = rgb.red;
            location+=4;
        }
    }else {
        var mode = this.width % 4;
        for (var y = this.height - 1; y >= 0; y--) {
            var line = this.bottom_up ? y : this.height - 1 - y
            for (var x = 0; x < this.width; x++) {
                var b = this.buffer.readUInt8(this.pos++);
                var location = line * this.width * 4 + x * 4;
                if (b < this.palette.length) {
                    var rgb = this.palette[b];

                    this.data[location] = 0;
                    this.data[location + 1] = rgb.blue;
                    this.data[location + 2] = rgb.green;
                    this.data[location + 3] = rgb.red;

                } else {
                    this.data[location] = 0;
                    this.data[location + 1] = 0xFF;
                    this.data[location + 2] = 0xFF;
                    this.data[location + 3] = 0xFF;
                }
            }
            if (mode != 0) {
                this.pos += (4 - mode);
            }
        }
    }
};

BmpDecoder.prototype.bit15 = function() {
  var dif_w =this.width % 3;
  var _11111 = parseInt("11111", 2),_1_5 = _11111;
  for (var y = this.height - 1; y >= 0; y--) {
    var line = this.bottom_up ? y : this.height - 1 - y
    for (var x = 0; x < this.width; x++) {

      var B = this.buffer.readUInt16LE(this.pos);
      this.pos+=2;
      var blue = (B & _1_5) / _1_5 * 255 | 0;
      var green = (B >> 5 & _1_5 ) / _1_5 * 255 | 0;
      var red = (B >> 10 & _1_5) / _1_5 * 255 | 0;
      var alpha = (B>>15)?0xFF:0x00;

      var location = line * this.width * 4 + x * 4;

      this.data[location] = alpha;
      this.data[location + 1] = blue;
      this.data[location + 2] = green;
      this.data[location + 3] = red;
    }
    //skip extra bytes
    this.pos += dif_w;
  }
};

BmpDecoder.prototype.bit16 = function() {
  var dif_w =(this.width % 2)*2;
  //default xrgb555
  this.maskRed = 0x7C00;
  this.maskGreen = 0x3E0;
  this.maskBlue =0x1F;
  this.mask0 = 0;

  if(this.compress == 3){
    this.maskRed = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
    this.maskGreen = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
    this.maskBlue = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
    this.mask0 = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
  }


  var ns=[0,0,0];
  for (var i=0;i<16;i++){
    if ((this.maskRed>>i)&0x01) ns[0]++;
    if ((this.maskGreen>>i)&0x01) ns[1]++;
    if ((this.maskBlue>>i)&0x01) ns[2]++;
  }
  ns[1]+=ns[0]; ns[2]+=ns[1];	ns[0]=8-ns[0]; ns[1]-=8; ns[2]-=8;

  for (var y = this.height - 1; y >= 0; y--) {
    var line = this.bottom_up ? y : this.height - 1 - y;
    for (var x = 0; x < this.width; x++) {

      var B = this.buffer.readUInt16LE(this.pos);
      this.pos+=2;

      var blue = (B&this.maskBlue)<<ns[0];
      var green = (B&this.maskGreen)>>ns[1];
      var red = (B&this.maskRed)>>ns[2];

      var location = line * this.width * 4 + x * 4;

      this.data[location] = 0;
      this.data[location + 1] = blue;
      this.data[location + 2] = green;
      this.data[location + 3] = red;
    }
    //skip extra bytes
    this.pos += dif_w;
  }
};

BmpDecoder.prototype.bit24 = function() {
  for (var y = this.height - 1; y >= 0; y--) {
    var line = this.bottom_up ? y : this.height - 1 - y
    for (var x = 0; x < this.width; x++) {
      //Little Endian rgb
      var blue = this.buffer.readUInt8(this.pos++);
      var green = this.buffer.readUInt8(this.pos++);
      var red = this.buffer.readUInt8(this.pos++);
      var location = line * this.width * 4 + x * 4;
      this.data[location] = 0;
      this.data[location + 1] = blue;
      this.data[location + 2] = green;
      this.data[location + 3] = red;
    }
    //skip extra bytes
    this.pos += (this.width % 4);
  }

};

/**
 * add 32bit decode func
 * <AUTHOR>
 */
BmpDecoder.prototype.bit32 = function() {
  //BI_BITFIELDS
  if(this.compress == 3){
    this.maskRed = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
    this.maskGreen = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
    this.maskBlue = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
    this.mask0 = this.buffer.readUInt32LE(this.pos);
    this.pos+=4;
      for (var y = this.height - 1; y >= 0; y--) {
          var line = this.bottom_up ? y : this.height - 1 - y;
          for (var x = 0; x < this.width; x++) {
              //Little Endian rgba
              var alpha = this.buffer.readUInt8(this.pos++);
              var blue = this.buffer.readUInt8(this.pos++);
              var green = this.buffer.readUInt8(this.pos++);
              var red = this.buffer.readUInt8(this.pos++);
              var location = line * this.width * 4 + x * 4;
              this.data[location] = alpha;
              this.data[location + 1] = blue;
              this.data[location + 2] = green;
              this.data[location + 3] = red;
          }
      }

  }else{
      for (var y = this.height - 1; y >= 0; y--) {
          var line = this.bottom_up ? y : this.height - 1 - y;
          for (var x = 0; x < this.width; x++) {
              //Little Endian argb
              var blue = this.buffer.readUInt8(this.pos++);
              var green = this.buffer.readUInt8(this.pos++);
              var red = this.buffer.readUInt8(this.pos++);
              var alpha = this.buffer.readUInt8(this.pos++);
              var location = line * this.width * 4 + x * 4;
              this.data[location] = alpha;
              this.data[location + 1] = blue;
              this.data[location + 2] = green;
              this.data[location + 3] = red;
          }
      }

  }




};

BmpDecoder.prototype.getData = function() {
  return this.data;
};

module.exports = function(bmpData) {
  var decoder = new BmpDecoder(bmpData);
  return decoder;
};

}).call(this)}).call(this,require("buffer").Buffer)
},{"buffer":15}],14:[function(require,module,exports){
(function (Buffer){(function (){
/**
 * <AUTHOR>
 *
 * BMP format encoder,encode 24bit BMP
 * Not support quality compression
 *
 */

function BmpEncoder(imgData){
	this.buffer = imgData.data;
	this.width = imgData.width;
	this.height = imgData.height;
	this.extraBytes = this.width%4;
	this.rgbSize = this.height*(3*this.width+this.extraBytes);
	this.headerInfoSize = 40;

	this.data = [];
	/******************header***********************/
	this.flag = "BM";
	this.reserved = 0;
	this.offset = 54;
	this.fileSize = this.rgbSize+this.offset;
	this.planes = 1;
	this.bitPP = 24;
	this.compress = 0;
	this.hr = 0;
	this.vr = 0;
	this.colors = 0;
	this.importantColors = 0;
}

BmpEncoder.prototype.encode = function() {
	var tempBuffer = new Buffer(this.offset+this.rgbSize);
	this.pos = 0;
	tempBuffer.write(this.flag,this.pos,2);this.pos+=2;
	tempBuffer.writeUInt32LE(this.fileSize,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.reserved,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.offset,this.pos);this.pos+=4;

	tempBuffer.writeUInt32LE(this.headerInfoSize,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.width,this.pos);this.pos+=4;
	tempBuffer.writeInt32LE(-this.height,this.pos);this.pos+=4;
	tempBuffer.writeUInt16LE(this.planes,this.pos);this.pos+=2;
	tempBuffer.writeUInt16LE(this.bitPP,this.pos);this.pos+=2;
	tempBuffer.writeUInt32LE(this.compress,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.rgbSize,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.hr,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.vr,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.colors,this.pos);this.pos+=4;
	tempBuffer.writeUInt32LE(this.importantColors,this.pos);this.pos+=4;

	var i=0;
	var rowBytes = 3*this.width+this.extraBytes;

	for (var y = 0; y <this.height; y++){
		for (var x = 0; x < this.width; x++){
			var p = this.pos+y*rowBytes+x*3;
			i++;//a
			tempBuffer[p]= this.buffer[i++];//b
			tempBuffer[p+1] = this.buffer[i++];//g
			tempBuffer[p+2]  = this.buffer[i++];//r
		}
		if(this.extraBytes>0){
			var fillOffset = this.pos+y*rowBytes+this.width*3;
			tempBuffer.fill(0,fillOffset,fillOffset+this.extraBytes);
		}
	}

	return tempBuffer;
};

module.exports = function(imgData, quality) {
  if (typeof quality === 'undefined') quality = 100;
 	var encoder = new BmpEncoder(imgData);
	var data = encoder.encode();
  return {
    data: data,
    width: imgData.width,
    height: imgData.height
  };
};

}).call(this)}).call(this,require("buffer").Buffer)
},{"buffer":15}],15:[function(require,module,exports){
(function (Buffer){(function (){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
/* eslint-disable no-proto */

'use strict'

var base64 = require('base64-js')
var ieee754 = require('ieee754')

exports.Buffer = Buffer
exports.SlowBuffer = SlowBuffer
exports.INSPECT_MAX_BYTES = 50

var K_MAX_LENGTH = 0x7fffffff
exports.kMaxLength = K_MAX_LENGTH

/**
 * If `Buffer.TYPED_ARRAY_SUPPORT`:
 *   === true    Use Uint8Array implementation (fastest)
 *   === false   Print warning and recommend using `buffer` v4.x which has an Object
 *               implementation (most compatible, even IE6)
 *
 * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
 * Opera 11.6+, iOS 4.2+.
 *
 * We report that the browser does not support typed arrays if the are not subclassable
 * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`
 * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support
 * for __proto__ and has a buggy typed array implementation.
 */
Buffer.TYPED_ARRAY_SUPPORT = typedArraySupport()

if (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&
    typeof console.error === 'function') {
  console.error(
    'This browser lacks typed array (Uint8Array) support which is required by ' +
    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'
  )
}

function typedArraySupport () {
  // Can typed array instances can be augmented?
  try {
    var arr = new Uint8Array(1)
    arr.__proto__ = { __proto__: Uint8Array.prototype, foo: function () { return 42 } }
    return arr.foo() === 42
  } catch (e) {
    return false
  }
}

Object.defineProperty(Buffer.prototype, 'parent', {
  enumerable: true,
  get: function () {
    if (!Buffer.isBuffer(this)) return undefined
    return this.buffer
  }
})

Object.defineProperty(Buffer.prototype, 'offset', {
  enumerable: true,
  get: function () {
    if (!Buffer.isBuffer(this)) return undefined
    return this.byteOffset
  }
})

function createBuffer (length) {
  if (length > K_MAX_LENGTH) {
    throw new RangeError('The value "' + length + '" is invalid for option "size"')
  }
  // Return an augmented `Uint8Array` instance
  var buf = new Uint8Array(length)
  buf.__proto__ = Buffer.prototype
  return buf
}

/**
 * The Buffer constructor returns instances of `Uint8Array` that have their
 * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
 * `Uint8Array`, so the returned instances will have all the node `Buffer` methods
 * and the `Uint8Array` methods. Square bracket notation works as expected -- it
 * returns a single octet.
 *
 * The `Uint8Array` prototype remains unmodified.
 */

function Buffer (arg, encodingOrOffset, length) {
  // Common case.
  if (typeof arg === 'number') {
    if (typeof encodingOrOffset === 'string') {
      throw new TypeError(
        'The "string" argument must be of type string. Received type number'
      )
    }
    return allocUnsafe(arg)
  }
  return from(arg, encodingOrOffset, length)
}

// Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97
if (typeof Symbol !== 'undefined' && Symbol.species != null &&
    Buffer[Symbol.species] === Buffer) {
  Object.defineProperty(Buffer, Symbol.species, {
    value: null,
    configurable: true,
    enumerable: false,
    writable: false
  })
}

Buffer.poolSize = 8192 // not used by this implementation

function from (value, encodingOrOffset, length) {
  if (typeof value === 'string') {
    return fromString(value, encodingOrOffset)
  }

  if (ArrayBuffer.isView(value)) {
    return fromArrayLike(value)
  }

  if (value == null) {
    throw TypeError(
      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
      'or Array-like Object. Received type ' + (typeof value)
    )
  }

  if (isInstance(value, ArrayBuffer) ||
      (value && isInstance(value.buffer, ArrayBuffer))) {
    return fromArrayBuffer(value, encodingOrOffset, length)
  }

  if (typeof value === 'number') {
    throw new TypeError(
      'The "value" argument must not be of type number. Received type number'
    )
  }

  var valueOf = value.valueOf && value.valueOf()
  if (valueOf != null && valueOf !== value) {
    return Buffer.from(valueOf, encodingOrOffset, length)
  }

  var b = fromObject(value)
  if (b) return b

  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&
      typeof value[Symbol.toPrimitive] === 'function') {
    return Buffer.from(
      value[Symbol.toPrimitive]('string'), encodingOrOffset, length
    )
  }

  throw new TypeError(
    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
    'or Array-like Object. Received type ' + (typeof value)
  )
}

/**
 * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
 * if value is a number.
 * Buffer.from(str[, encoding])
 * Buffer.from(array)
 * Buffer.from(buffer)
 * Buffer.from(arrayBuffer[, byteOffset[, length]])
 **/
Buffer.from = function (value, encodingOrOffset, length) {
  return from(value, encodingOrOffset, length)
}

// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:
// https://github.com/feross/buffer/pull/148
Buffer.prototype.__proto__ = Uint8Array.prototype
Buffer.__proto__ = Uint8Array

function assertSize (size) {
  if (typeof size !== 'number') {
    throw new TypeError('"size" argument must be of type number')
  } else if (size < 0) {
    throw new RangeError('The value "' + size + '" is invalid for option "size"')
  }
}

function alloc (size, fill, encoding) {
  assertSize(size)
  if (size <= 0) {
    return createBuffer(size)
  }
  if (fill !== undefined) {
    // Only pay attention to encoding if it's a string. This
    // prevents accidentally sending in a number that would
    // be interpretted as a start offset.
    return typeof encoding === 'string'
      ? createBuffer(size).fill(fill, encoding)
      : createBuffer(size).fill(fill)
  }
  return createBuffer(size)
}

/**
 * Creates a new filled Buffer instance.
 * alloc(size[, fill[, encoding]])
 **/
Buffer.alloc = function (size, fill, encoding) {
  return alloc(size, fill, encoding)
}

function allocUnsafe (size) {
  assertSize(size)
  return createBuffer(size < 0 ? 0 : checked(size) | 0)
}

/**
 * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
 * */
Buffer.allocUnsafe = function (size) {
  return allocUnsafe(size)
}
/**
 * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
 */
Buffer.allocUnsafeSlow = function (size) {
  return allocUnsafe(size)
}

function fromString (string, encoding) {
  if (typeof encoding !== 'string' || encoding === '') {
    encoding = 'utf8'
  }

  if (!Buffer.isEncoding(encoding)) {
    throw new TypeError('Unknown encoding: ' + encoding)
  }

  var length = byteLength(string, encoding) | 0
  var buf = createBuffer(length)

  var actual = buf.write(string, encoding)

  if (actual !== length) {
    // Writing a hex string, for example, that contains invalid characters will
    // cause everything after the first invalid character to be ignored. (e.g.
    // 'abxxcd' will be treated as 'ab')
    buf = buf.slice(0, actual)
  }

  return buf
}

function fromArrayLike (array) {
  var length = array.length < 0 ? 0 : checked(array.length) | 0
  var buf = createBuffer(length)
  for (var i = 0; i < length; i += 1) {
    buf[i] = array[i] & 255
  }
  return buf
}

function fromArrayBuffer (array, byteOffset, length) {
  if (byteOffset < 0 || array.byteLength < byteOffset) {
    throw new RangeError('"offset" is outside of buffer bounds')
  }

  if (array.byteLength < byteOffset + (length || 0)) {
    throw new RangeError('"length" is outside of buffer bounds')
  }

  var buf
  if (byteOffset === undefined && length === undefined) {
    buf = new Uint8Array(array)
  } else if (length === undefined) {
    buf = new Uint8Array(array, byteOffset)
  } else {
    buf = new Uint8Array(array, byteOffset, length)
  }

  // Return an augmented `Uint8Array` instance
  buf.__proto__ = Buffer.prototype
  return buf
}

function fromObject (obj) {
  if (Buffer.isBuffer(obj)) {
    var len = checked(obj.length) | 0
    var buf = createBuffer(len)

    if (buf.length === 0) {
      return buf
    }

    obj.copy(buf, 0, 0, len)
    return buf
  }

  if (obj.length !== undefined) {
    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {
      return createBuffer(0)
    }
    return fromArrayLike(obj)
  }

  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {
    return fromArrayLike(obj.data)
  }
}

function checked (length) {
  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when
  // length is NaN (which is otherwise coerced to zero.)
  if (length >= K_MAX_LENGTH) {
    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +
                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')
  }
  return length | 0
}

function SlowBuffer (length) {
  if (+length != length) { // eslint-disable-line eqeqeq
    length = 0
  }
  return Buffer.alloc(+length)
}

Buffer.isBuffer = function isBuffer (b) {
  return b != null && b._isBuffer === true &&
    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false
}

Buffer.compare = function compare (a, b) {
  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)
  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)
  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {
    throw new TypeError(
      'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array'
    )
  }

  if (a === b) return 0

  var x = a.length
  var y = b.length

  for (var i = 0, len = Math.min(x, y); i < len; ++i) {
    if (a[i] !== b[i]) {
      x = a[i]
      y = b[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

Buffer.isEncoding = function isEncoding (encoding) {
  switch (String(encoding).toLowerCase()) {
    case 'hex':
    case 'utf8':
    case 'utf-8':
    case 'ascii':
    case 'latin1':
    case 'binary':
    case 'base64':
    case 'ucs2':
    case 'ucs-2':
    case 'utf16le':
    case 'utf-16le':
      return true
    default:
      return false
  }
}

Buffer.concat = function concat (list, length) {
  if (!Array.isArray(list)) {
    throw new TypeError('"list" argument must be an Array of Buffers')
  }

  if (list.length === 0) {
    return Buffer.alloc(0)
  }

  var i
  if (length === undefined) {
    length = 0
    for (i = 0; i < list.length; ++i) {
      length += list[i].length
    }
  }

  var buffer = Buffer.allocUnsafe(length)
  var pos = 0
  for (i = 0; i < list.length; ++i) {
    var buf = list[i]
    if (isInstance(buf, Uint8Array)) {
      buf = Buffer.from(buf)
    }
    if (!Buffer.isBuffer(buf)) {
      throw new TypeError('"list" argument must be an Array of Buffers')
    }
    buf.copy(buffer, pos)
    pos += buf.length
  }
  return buffer
}

function byteLength (string, encoding) {
  if (Buffer.isBuffer(string)) {
    return string.length
  }
  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {
    return string.byteLength
  }
  if (typeof string !== 'string') {
    throw new TypeError(
      'The "string" argument must be one of type string, Buffer, or ArrayBuffer. ' +
      'Received type ' + typeof string
    )
  }

  var len = string.length
  var mustMatch = (arguments.length > 2 && arguments[2] === true)
  if (!mustMatch && len === 0) return 0

  // Use a for loop to avoid recursion
  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'ascii':
      case 'latin1':
      case 'binary':
        return len
      case 'utf8':
      case 'utf-8':
        return utf8ToBytes(string).length
      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return len * 2
      case 'hex':
        return len >>> 1
      case 'base64':
        return base64ToBytes(string).length
      default:
        if (loweredCase) {
          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8
        }
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}
Buffer.byteLength = byteLength

function slowToString (encoding, start, end) {
  var loweredCase = false

  // No need to verify that "this.length <= MAX_UINT32" since it's a read-only
  // property of a typed array.

  // This behaves neither like String nor Uint8Array in that we set start/end
  // to their upper/lower bounds if the value passed is out of range.
  // undefined is handled specially as per ECMA-262 6th Edition,
  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.
  if (start === undefined || start < 0) {
    start = 0
  }
  // Return early if start > this.length. Done here to prevent potential uint32
  // coercion fail below.
  if (start > this.length) {
    return ''
  }

  if (end === undefined || end > this.length) {
    end = this.length
  }

  if (end <= 0) {
    return ''
  }

  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.
  end >>>= 0
  start >>>= 0

  if (end <= start) {
    return ''
  }

  if (!encoding) encoding = 'utf8'

  while (true) {
    switch (encoding) {
      case 'hex':
        return hexSlice(this, start, end)

      case 'utf8':
      case 'utf-8':
        return utf8Slice(this, start, end)

      case 'ascii':
        return asciiSlice(this, start, end)

      case 'latin1':
      case 'binary':
        return latin1Slice(this, start, end)

      case 'base64':
        return base64Slice(this, start, end)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return utf16leSlice(this, start, end)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = (encoding + '').toLowerCase()
        loweredCase = true
    }
  }
}

// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)
// to detect a Buffer instance. It's not possible to use `instanceof Buffer`
// reliably in a browserify context because there could be multiple different
// copies of the 'buffer' package in use. This method works even for Buffer
// instances that were created from another copy of the `buffer` package.
// See: https://github.com/feross/buffer/issues/154
Buffer.prototype._isBuffer = true

function swap (b, n, m) {
  var i = b[n]
  b[n] = b[m]
  b[m] = i
}

Buffer.prototype.swap16 = function swap16 () {
  var len = this.length
  if (len % 2 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 16-bits')
  }
  for (var i = 0; i < len; i += 2) {
    swap(this, i, i + 1)
  }
  return this
}

Buffer.prototype.swap32 = function swap32 () {
  var len = this.length
  if (len % 4 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 32-bits')
  }
  for (var i = 0; i < len; i += 4) {
    swap(this, i, i + 3)
    swap(this, i + 1, i + 2)
  }
  return this
}

Buffer.prototype.swap64 = function swap64 () {
  var len = this.length
  if (len % 8 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 64-bits')
  }
  for (var i = 0; i < len; i += 8) {
    swap(this, i, i + 7)
    swap(this, i + 1, i + 6)
    swap(this, i + 2, i + 5)
    swap(this, i + 3, i + 4)
  }
  return this
}

Buffer.prototype.toString = function toString () {
  var length = this.length
  if (length === 0) return ''
  if (arguments.length === 0) return utf8Slice(this, 0, length)
  return slowToString.apply(this, arguments)
}

Buffer.prototype.toLocaleString = Buffer.prototype.toString

Buffer.prototype.equals = function equals (b) {
  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')
  if (this === b) return true
  return Buffer.compare(this, b) === 0
}

Buffer.prototype.inspect = function inspect () {
  var str = ''
  var max = exports.INSPECT_MAX_BYTES
  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()
  if (this.length > max) str += ' ... '
  return '<Buffer ' + str + '>'
}

Buffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {
  if (isInstance(target, Uint8Array)) {
    target = Buffer.from(target, target.offset, target.byteLength)
  }
  if (!Buffer.isBuffer(target)) {
    throw new TypeError(
      'The "target" argument must be one of type Buffer or Uint8Array. ' +
      'Received type ' + (typeof target)
    )
  }

  if (start === undefined) {
    start = 0
  }
  if (end === undefined) {
    end = target ? target.length : 0
  }
  if (thisStart === undefined) {
    thisStart = 0
  }
  if (thisEnd === undefined) {
    thisEnd = this.length
  }

  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
    throw new RangeError('out of range index')
  }

  if (thisStart >= thisEnd && start >= end) {
    return 0
  }
  if (thisStart >= thisEnd) {
    return -1
  }
  if (start >= end) {
    return 1
  }

  start >>>= 0
  end >>>= 0
  thisStart >>>= 0
  thisEnd >>>= 0

  if (this === target) return 0

  var x = thisEnd - thisStart
  var y = end - start
  var len = Math.min(x, y)

  var thisCopy = this.slice(thisStart, thisEnd)
  var targetCopy = target.slice(start, end)

  for (var i = 0; i < len; ++i) {
    if (thisCopy[i] !== targetCopy[i]) {
      x = thisCopy[i]
      y = targetCopy[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,
// OR the last index of `val` in `buffer` at offset <= `byteOffset`.
//
// Arguments:
// - buffer - a Buffer to search
// - val - a string, Buffer, or number
// - byteOffset - an index into `buffer`; will be clamped to an int32
// - encoding - an optional encoding, relevant is val is a string
// - dir - true for indexOf, false for lastIndexOf
function bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {
  // Empty buffer means no match
  if (buffer.length === 0) return -1

  // Normalize byteOffset
  if (typeof byteOffset === 'string') {
    encoding = byteOffset
    byteOffset = 0
  } else if (byteOffset > 0x7fffffff) {
    byteOffset = 0x7fffffff
  } else if (byteOffset < -0x80000000) {
    byteOffset = -0x80000000
  }
  byteOffset = +byteOffset // Coerce to Number.
  if (numberIsNaN(byteOffset)) {
    // byteOffset: it it's undefined, null, NaN, "foo", etc, search whole buffer
    byteOffset = dir ? 0 : (buffer.length - 1)
  }

  // Normalize byteOffset: negative offsets start from the end of the buffer
  if (byteOffset < 0) byteOffset = buffer.length + byteOffset
  if (byteOffset >= buffer.length) {
    if (dir) return -1
    else byteOffset = buffer.length - 1
  } else if (byteOffset < 0) {
    if (dir) byteOffset = 0
    else return -1
  }

  // Normalize val
  if (typeof val === 'string') {
    val = Buffer.from(val, encoding)
  }

  // Finally, search either indexOf (if dir is true) or lastIndexOf
  if (Buffer.isBuffer(val)) {
    // Special case: looking for empty string/buffer always fails
    if (val.length === 0) {
      return -1
    }
    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)
  } else if (typeof val === 'number') {
    val = val & 0xFF // Search for a byte value [0-255]
    if (typeof Uint8Array.prototype.indexOf === 'function') {
      if (dir) {
        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)
      } else {
        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)
      }
    }
    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)
  }

  throw new TypeError('val must be string, number or Buffer')
}

function arrayIndexOf (arr, val, byteOffset, encoding, dir) {
  var indexSize = 1
  var arrLength = arr.length
  var valLength = val.length

  if (encoding !== undefined) {
    encoding = String(encoding).toLowerCase()
    if (encoding === 'ucs2' || encoding === 'ucs-2' ||
        encoding === 'utf16le' || encoding === 'utf-16le') {
      if (arr.length < 2 || val.length < 2) {
        return -1
      }
      indexSize = 2
      arrLength /= 2
      valLength /= 2
      byteOffset /= 2
    }
  }

  function read (buf, i) {
    if (indexSize === 1) {
      return buf[i]
    } else {
      return buf.readUInt16BE(i * indexSize)
    }
  }

  var i
  if (dir) {
    var foundIndex = -1
    for (i = byteOffset; i < arrLength; i++) {
      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
        if (foundIndex === -1) foundIndex = i
        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize
      } else {
        if (foundIndex !== -1) i -= i - foundIndex
        foundIndex = -1
      }
    }
  } else {
    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength
    for (i = byteOffset; i >= 0; i--) {
      var found = true
      for (var j = 0; j < valLength; j++) {
        if (read(arr, i + j) !== read(val, j)) {
          found = false
          break
        }
      }
      if (found) return i
    }
  }

  return -1
}

Buffer.prototype.includes = function includes (val, byteOffset, encoding) {
  return this.indexOf(val, byteOffset, encoding) !== -1
}

Buffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)
}

Buffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)
}

function hexWrite (buf, string, offset, length) {
  offset = Number(offset) || 0
  var remaining = buf.length - offset
  if (!length) {
    length = remaining
  } else {
    length = Number(length)
    if (length > remaining) {
      length = remaining
    }
  }

  var strLen = string.length

  if (length > strLen / 2) {
    length = strLen / 2
  }
  for (var i = 0; i < length; ++i) {
    var parsed = parseInt(string.substr(i * 2, 2), 16)
    if (numberIsNaN(parsed)) return i
    buf[offset + i] = parsed
  }
  return i
}

function utf8Write (buf, string, offset, length) {
  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)
}

function asciiWrite (buf, string, offset, length) {
  return blitBuffer(asciiToBytes(string), buf, offset, length)
}

function latin1Write (buf, string, offset, length) {
  return asciiWrite(buf, string, offset, length)
}

function base64Write (buf, string, offset, length) {
  return blitBuffer(base64ToBytes(string), buf, offset, length)
}

function ucs2Write (buf, string, offset, length) {
  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)
}

Buffer.prototype.write = function write (string, offset, length, encoding) {
  // Buffer#write(string)
  if (offset === undefined) {
    encoding = 'utf8'
    length = this.length
    offset = 0
  // Buffer#write(string, encoding)
  } else if (length === undefined && typeof offset === 'string') {
    encoding = offset
    length = this.length
    offset = 0
  // Buffer#write(string, offset[, length][, encoding])
  } else if (isFinite(offset)) {
    offset = offset >>> 0
    if (isFinite(length)) {
      length = length >>> 0
      if (encoding === undefined) encoding = 'utf8'
    } else {
      encoding = length
      length = undefined
    }
  } else {
    throw new Error(
      'Buffer.write(string, encoding, offset[, length]) is no longer supported'
    )
  }

  var remaining = this.length - offset
  if (length === undefined || length > remaining) length = remaining

  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {
    throw new RangeError('Attempt to write outside buffer bounds')
  }

  if (!encoding) encoding = 'utf8'

  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'hex':
        return hexWrite(this, string, offset, length)

      case 'utf8':
      case 'utf-8':
        return utf8Write(this, string, offset, length)

      case 'ascii':
        return asciiWrite(this, string, offset, length)

      case 'latin1':
      case 'binary':
        return latin1Write(this, string, offset, length)

      case 'base64':
        // Warning: maxLength not taken into account in base64Write
        return base64Write(this, string, offset, length)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return ucs2Write(this, string, offset, length)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}

Buffer.prototype.toJSON = function toJSON () {
  return {
    type: 'Buffer',
    data: Array.prototype.slice.call(this._arr || this, 0)
  }
}

function base64Slice (buf, start, end) {
  if (start === 0 && end === buf.length) {
    return base64.fromByteArray(buf)
  } else {
    return base64.fromByteArray(buf.slice(start, end))
  }
}

function utf8Slice (buf, start, end) {
  end = Math.min(buf.length, end)
  var res = []

  var i = start
  while (i < end) {
    var firstByte = buf[i]
    var codePoint = null
    var bytesPerSequence = (firstByte > 0xEF) ? 4
      : (firstByte > 0xDF) ? 3
        : (firstByte > 0xBF) ? 2
          : 1

    if (i + bytesPerSequence <= end) {
      var secondByte, thirdByte, fourthByte, tempCodePoint

      switch (bytesPerSequence) {
        case 1:
          if (firstByte < 0x80) {
            codePoint = firstByte
          }
          break
        case 2:
          secondByte = buf[i + 1]
          if ((secondByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)
            if (tempCodePoint > 0x7F) {
              codePoint = tempCodePoint
            }
          }
          break
        case 3:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)
            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {
              codePoint = tempCodePoint
            }
          }
          break
        case 4:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          fourthByte = buf[i + 3]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)
            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {
              codePoint = tempCodePoint
            }
          }
      }
    }

    if (codePoint === null) {
      // we did not generate a valid codePoint so insert a
      // replacement char (U+FFFD) and advance only 1 byte
      codePoint = 0xFFFD
      bytesPerSequence = 1
    } else if (codePoint > 0xFFFF) {
      // encode to utf16 (surrogate pair dance)
      codePoint -= 0x10000
      res.push(codePoint >>> 10 & 0x3FF | 0xD800)
      codePoint = 0xDC00 | codePoint & 0x3FF
    }

    res.push(codePoint)
    i += bytesPerSequence
  }

  return decodeCodePointsArray(res)
}

// Based on http://stackoverflow.com/a/22747272/680742, the browser with
// the lowest limit is Chrome, with 0x10000 args.
// We go 1 magnitude less, for safety
var MAX_ARGUMENTS_LENGTH = 0x1000

function decodeCodePointsArray (codePoints) {
  var len = codePoints.length
  if (len <= MAX_ARGUMENTS_LENGTH) {
    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()
  }

  // Decode in chunks to avoid "call stack size exceeded".
  var res = ''
  var i = 0
  while (i < len) {
    res += String.fromCharCode.apply(
      String,
      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)
    )
  }
  return res
}

function asciiSlice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i] & 0x7F)
  }
  return ret
}

function latin1Slice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i])
  }
  return ret
}

function hexSlice (buf, start, end) {
  var len = buf.length

  if (!start || start < 0) start = 0
  if (!end || end < 0 || end > len) end = len

  var out = ''
  for (var i = start; i < end; ++i) {
    out += toHex(buf[i])
  }
  return out
}

function utf16leSlice (buf, start, end) {
  var bytes = buf.slice(start, end)
  var res = ''
  for (var i = 0; i < bytes.length; i += 2) {
    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))
  }
  return res
}

Buffer.prototype.slice = function slice (start, end) {
  var len = this.length
  start = ~~start
  end = end === undefined ? len : ~~end

  if (start < 0) {
    start += len
    if (start < 0) start = 0
  } else if (start > len) {
    start = len
  }

  if (end < 0) {
    end += len
    if (end < 0) end = 0
  } else if (end > len) {
    end = len
  }

  if (end < start) end = start

  var newBuf = this.subarray(start, end)
  // Return an augmented `Uint8Array` instance
  newBuf.__proto__ = Buffer.prototype
  return newBuf
}

/*
 * Need to make sure that buffer isn't trying to write out of bounds.
 */
function checkOffset (offset, ext, length) {
  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')
  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')
}

Buffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }

  return val
}

Buffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    checkOffset(offset, byteLength, this.length)
  }

  var val = this[offset + --byteLength]
  var mul = 1
  while (byteLength > 0 && (mul *= 0x100)) {
    val += this[offset + --byteLength] * mul
  }

  return val
}

Buffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 1, this.length)
  return this[offset]
}

Buffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  return this[offset] | (this[offset + 1] << 8)
}

Buffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  return (this[offset] << 8) | this[offset + 1]
}

Buffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return ((this[offset]) |
      (this[offset + 1] << 8) |
      (this[offset + 2] << 16)) +
      (this[offset + 3] * 0x1000000)
}

Buffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] * 0x1000000) +
    ((this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    this[offset + 3])
}

Buffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var i = byteLength
  var mul = 1
  var val = this[offset + --i]
  while (i > 0 && (mul *= 0x100)) {
    val += this[offset + --i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readInt8 = function readInt8 (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 1, this.length)
  if (!(this[offset] & 0x80)) return (this[offset])
  return ((0xff - this[offset] + 1) * -1)
}

Buffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset] | (this[offset + 1] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset + 1] | (this[offset] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset]) |
    (this[offset + 1] << 8) |
    (this[offset + 2] << 16) |
    (this[offset + 3] << 24)
}

Buffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] << 24) |
    (this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    (this[offset + 3])
}

Buffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, true, 23, 4)
}

Buffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, false, 23, 4)
}

Buffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, true, 52, 8)
}

Buffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {
  offset = offset >>> 0
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, false, 52, 8)
}

function checkInt (buf, value, offset, ext, max, min) {
  if (!Buffer.isBuffer(buf)) throw new TypeError('"buffer" argument must be a Buffer instance')
  if (value > max || value < min) throw new RangeError('"value" argument is out of bounds')
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
}

Buffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var mul = 1
  var i = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  byteLength = byteLength >>> 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var i = byteLength - 1
  var mul = 1
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  return offset + 2
}

Buffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  this[offset] = (value >>> 8)
  this[offset + 1] = (value & 0xff)
  return offset + 2
}

Buffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  this[offset + 3] = (value >>> 24)
  this[offset + 2] = (value >>> 16)
  this[offset + 1] = (value >>> 8)
  this[offset] = (value & 0xff)
  return offset + 4
}

Buffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  this[offset] = (value >>> 24)
  this[offset + 1] = (value >>> 16)
  this[offset + 2] = (value >>> 8)
  this[offset + 3] = (value & 0xff)
  return offset + 4
}

Buffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    var limit = Math.pow(2, (8 * byteLength) - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = 0
  var mul = 1
  var sub = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    var limit = Math.pow(2, (8 * byteLength) - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = byteLength - 1
  var mul = 1
  var sub = 0
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)
  if (value < 0) value = 0xff + value + 1
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  return offset + 2
}

Buffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  this[offset] = (value >>> 8)
  this[offset + 1] = (value & 0xff)
  return offset + 2
}

Buffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  this[offset] = (value & 0xff)
  this[offset + 1] = (value >>> 8)
  this[offset + 2] = (value >>> 16)
  this[offset + 3] = (value >>> 24)
  return offset + 4
}

Buffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  if (value < 0) value = 0xffffffff + value + 1
  this[offset] = (value >>> 24)
  this[offset + 1] = (value >>> 16)
  this[offset + 2] = (value >>> 8)
  this[offset + 3] = (value & 0xff)
  return offset + 4
}

function checkIEEE754 (buf, value, offset, ext, max, min) {
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
  if (offset < 0) throw new RangeError('Index out of range')
}

function writeFloat (buf, value, offset, littleEndian, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)
  }
  ieee754.write(buf, value, offset, littleEndian, 23, 4)
  return offset + 4
}

Buffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {
  return writeFloat(this, value, offset, true, noAssert)
}

Buffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {
  return writeFloat(this, value, offset, false, noAssert)
}

function writeDouble (buf, value, offset, littleEndian, noAssert) {
  value = +value
  offset = offset >>> 0
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)
  }
  ieee754.write(buf, value, offset, littleEndian, 52, 8)
  return offset + 8
}

Buffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {
  return writeDouble(this, value, offset, true, noAssert)
}

Buffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {
  return writeDouble(this, value, offset, false, noAssert)
}

// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)
Buffer.prototype.copy = function copy (target, targetStart, start, end) {
  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')
  if (!start) start = 0
  if (!end && end !== 0) end = this.length
  if (targetStart >= target.length) targetStart = target.length
  if (!targetStart) targetStart = 0
  if (end > 0 && end < start) end = start

  // Copy 0 bytes; we're done
  if (end === start) return 0
  if (target.length === 0 || this.length === 0) return 0

  // Fatal error conditions
  if (targetStart < 0) {
    throw new RangeError('targetStart out of bounds')
  }
  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')
  if (end < 0) throw new RangeError('sourceEnd out of bounds')

  // Are we oob?
  if (end > this.length) end = this.length
  if (target.length - targetStart < end - start) {
    end = target.length - targetStart + start
  }

  var len = end - start

  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {
    // Use built-in when available, missing from IE11
    this.copyWithin(targetStart, start, end)
  } else if (this === target && start < targetStart && targetStart < end) {
    // descending copy from end
    for (var i = len - 1; i >= 0; --i) {
      target[i + targetStart] = this[i + start]
    }
  } else {
    Uint8Array.prototype.set.call(
      target,
      this.subarray(start, end),
      targetStart
    )
  }

  return len
}

// Usage:
//    buffer.fill(number[, offset[, end]])
//    buffer.fill(buffer[, offset[, end]])
//    buffer.fill(string[, offset[, end]][, encoding])
Buffer.prototype.fill = function fill (val, start, end, encoding) {
  // Handle string cases:
  if (typeof val === 'string') {
    if (typeof start === 'string') {
      encoding = start
      start = 0
      end = this.length
    } else if (typeof end === 'string') {
      encoding = end
      end = this.length
    }
    if (encoding !== undefined && typeof encoding !== 'string') {
      throw new TypeError('encoding must be a string')
    }
    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {
      throw new TypeError('Unknown encoding: ' + encoding)
    }
    if (val.length === 1) {
      var code = val.charCodeAt(0)
      if ((encoding === 'utf8' && code < 128) ||
          encoding === 'latin1') {
        // Fast path: If `val` fits into a single byte, use that numeric value.
        val = code
      }
    }
  } else if (typeof val === 'number') {
    val = val & 255
  }

  // Invalid ranges are not set to a default, so can range check early.
  if (start < 0 || this.length < start || this.length < end) {
    throw new RangeError('Out of range index')
  }

  if (end <= start) {
    return this
  }

  start = start >>> 0
  end = end === undefined ? this.length : end >>> 0

  if (!val) val = 0

  var i
  if (typeof val === 'number') {
    for (i = start; i < end; ++i) {
      this[i] = val
    }
  } else {
    var bytes = Buffer.isBuffer(val)
      ? val
      : Buffer.from(val, encoding)
    var len = bytes.length
    if (len === 0) {
      throw new TypeError('The value "' + val +
        '" is invalid for argument "value"')
    }
    for (i = 0; i < end - start; ++i) {
      this[i + start] = bytes[i % len]
    }
  }

  return this
}

// HELPER FUNCTIONS
// ================

var INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g

function base64clean (str) {
  // Node takes equal signs as end of the Base64 encoding
  str = str.split('=')[0]
  // Node strips out invalid characters like \n and \t from the string, base64-js does not
  str = str.trim().replace(INVALID_BASE64_RE, '')
  // Node converts strings with length < 2 to ''
  if (str.length < 2) return ''
  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not
  while (str.length % 4 !== 0) {
    str = str + '='
  }
  return str
}

function toHex (n) {
  if (n < 16) return '0' + n.toString(16)
  return n.toString(16)
}

function utf8ToBytes (string, units) {
  units = units || Infinity
  var codePoint
  var length = string.length
  var leadSurrogate = null
  var bytes = []

  for (var i = 0; i < length; ++i) {
    codePoint = string.charCodeAt(i)

    // is surrogate component
    if (codePoint > 0xD7FF && codePoint < 0xE000) {
      // last char was a lead
      if (!leadSurrogate) {
        // no lead yet
        if (codePoint > 0xDBFF) {
          // unexpected trail
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        } else if (i + 1 === length) {
          // unpaired lead
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        }

        // valid lead
        leadSurrogate = codePoint

        continue
      }

      // 2 leads in a row
      if (codePoint < 0xDC00) {
        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
        leadSurrogate = codePoint
        continue
      }

      // valid surrogate pair
      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000
    } else if (leadSurrogate) {
      // valid bmp char, but last char was a lead
      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
    }

    leadSurrogate = null

    // encode utf8
    if (codePoint < 0x80) {
      if ((units -= 1) < 0) break
      bytes.push(codePoint)
    } else if (codePoint < 0x800) {
      if ((units -= 2) < 0) break
      bytes.push(
        codePoint >> 0x6 | 0xC0,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x10000) {
      if ((units -= 3) < 0) break
      bytes.push(
        codePoint >> 0xC | 0xE0,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x110000) {
      if ((units -= 4) < 0) break
      bytes.push(
        codePoint >> 0x12 | 0xF0,
        codePoint >> 0xC & 0x3F | 0x80,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else {
      throw new Error('Invalid code point')
    }
  }

  return bytes
}

function asciiToBytes (str) {
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    // Node's code seems to be doing this and not & 0x7F..
    byteArray.push(str.charCodeAt(i) & 0xFF)
  }
  return byteArray
}

function utf16leToBytes (str, units) {
  var c, hi, lo
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    if ((units -= 2) < 0) break

    c = str.charCodeAt(i)
    hi = c >> 8
    lo = c % 256
    byteArray.push(lo)
    byteArray.push(hi)
  }

  return byteArray
}

function base64ToBytes (str) {
  return base64.toByteArray(base64clean(str))
}

function blitBuffer (src, dst, offset, length) {
  for (var i = 0; i < length; ++i) {
    if ((i + offset >= dst.length) || (i >= src.length)) break
    dst[i + offset] = src[i]
  }
  return i
}

// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass
// the `instanceof` check but they should be treated as of that type.
// See: https://github.com/feross/buffer/issues/166
function isInstance (obj, type) {
  return obj instanceof type ||
    (obj != null && obj.constructor != null && obj.constructor.name != null &&
      obj.constructor.name === type.name)
}
function numberIsNaN (obj) {
  // For IE11 support
  return obj !== obj // eslint-disable-line no-self-compare
}

}).call(this)}).call(this,require("buffer").Buffer)
},{"base64-js":11,"buffer":15,"ieee754":16}],16:[function(require,module,exports){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
exports.read = function (buffer, offset, isLE, mLen, nBytes) {
  var e, m
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var nBits = -7
  var i = isLE ? (nBytes - 1) : 0
  var d = isLE ? -1 : 1
  var s = buffer[offset + i]

  i += d

  e = s & ((1 << (-nBits)) - 1)
  s >>= (-nBits)
  nBits += eLen
  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  m = e & ((1 << (-nBits)) - 1)
  e >>= (-nBits)
  nBits += mLen
  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}

  if (e === 0) {
    e = 1 - eBias
  } else if (e === eMax) {
    return m ? NaN : ((s ? -1 : 1) * Infinity)
  } else {
    m = m + Math.pow(2, mLen)
    e = e - eBias
  }
  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)
}

exports.write = function (buffer, value, offset, isLE, mLen, nBytes) {
  var e, m, c
  var eLen = (nBytes * 8) - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)
  var i = isLE ? 0 : (nBytes - 1)
  var d = isLE ? 1 : -1
  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0

  value = Math.abs(value)

  if (isNaN(value) || value === Infinity) {
    m = isNaN(value) ? 1 : 0
    e = eMax
  } else {
    e = Math.floor(Math.log(value) / Math.LN2)
    if (value * (c = Math.pow(2, -e)) < 1) {
      e--
      c *= 2
    }
    if (e + eBias >= 1) {
      value += rt / c
    } else {
      value += rt * Math.pow(2, 1 - eBias)
    }
    if (value * c >= 2) {
      e++
      c /= 2
    }

    if (e + eBias >= eMax) {
      m = 0
      e = eMax
    } else if (e + eBias >= 1) {
      m = ((value * c) - 1) * Math.pow(2, mLen)
      e = e + eBias
    } else {
      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)
      e = 0
    }
  }

  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}

  e = (e << mLen) | m
  eLen += mLen
  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}

  buffer[offset + i - d] |= s * 128
}

},{}]},{},[7]);
