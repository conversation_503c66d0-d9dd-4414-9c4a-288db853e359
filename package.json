{"name": "marklife-label-printer-web-kit", "version": "1.0.4", "description": "JavaScript library for BLE label printing with Marklife printers", "main": "lib/index.js", "files": ["lib/", "README.md", "LICENSE"], "keywords": ["BLE", "label-printer", "Mark<PERSON>", "browser", "node"], "author": "MickeyGR (Atokatl)", "license": "SEE LICENSE IN LICENSE", "homepage": "https://atokatl.dev", "repository": {"type": "git", "url": "https://gitlab.com/marklife/marklife-label-printer-web-kit.git"}, "dependencies": {"bmp-js": "^0.1.0"}, "devDependencies": {"browserify": "^17.0.1"}, "scripts": {"build:browser": "browserify lib/index.js -o dist/marklife.bundle.js"}}